#!/usr/bin/env python3
"""
基因组特征提取器 - 优化版

主要优化：
1. 性能提升：内存映射处理大型文件，并行化序列提取
2. 健壮性增强：完善的注释解析，错误恢复机制
3. 功能扩展：预测工具检查，序列ID规范化，结果缓存
4. 资源管理：智能内存使用，临时文件优化

作者：Cline
日期：2025-08-06
版本：2.0 (优化版)
"""

import os
import sys
import csv
import gzip
import json
import logging
import argparse
import multiprocessing
import subprocess
import tempfile
import shutil
import re
import mmap
import psutil
import time
import urllib.request
import urllib.error
import io
from pathlib import Path
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Optional, Any

# 导入NCBI下载器
try:
    from ncbi_downloader import NCBIDownloader
    NCBI_DOWNLOADER_AVAILABLE = True
except ImportError:
    NCBI_DOWNLOADER_AVAILABLE = False
    print("警告: 未找到NCBI下载器，将使用内置下载功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('genome_feature_extractor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 生物界分类目录映射
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi',
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

# 预测工具映射
PREDICTION_TOOL_MAPPING = {
    'CDS': {
        'Bacteria': 'prodigal',
        'Archaea': 'prodigal',
        'Eukaryota': 'augustus',
        'Fungi': 'augustus',
        'Algae': 'augustus',
        'Plant': 'augustus',
        'Protist': 'augustus',
        'default': 'prokka'
    },
    'tRNA': {
        'default': 'trnascan'
    },
    'rRNA': {
        'default': 'barrnap'
    }
}

# 注释文件扩展名优先级
ANNOTATION_EXTENSIONS = [
    '.gff.gz', '.gff', 
    '.gtf.gz', '.gtf',
    '.gbff.gz', '.gbff',
    '.gff3.gz', '.gff3'
]

# 序列文件扩展名
SEQUENCE_EXTENSIONS = [
    '.fna.gz', '.fa.gz', '.fasta.gz', 
    '.fna', '.fa', '.fasta',
    '.fsa', '.seq'
]

class GenomeFeatureExtractor:
    def __init__(self, root_dir=None, output_dir='extracted_features', threads=4, 
                 enable_prediction=True, skip_prediction=False, cache_dir=None,
                 ncbi_email=None, ncbi_api_key=None):
        """
        初始化基因组特征提取器
        
        Args:
            root_dir: 基因组数据根目录
            output_dir: 输出目录
            threads: 线程数
            enable_prediction: 是否启用特征预测
            skip_prediction: 是否跳过预测（仅使用现有注释）
            cache_dir: 预测结果缓存目录
            ncbi_email: NCBI下载器邮箱
            ncbi_api_key: NCBI下载器API密钥
        """
        self.root_dir = Path(root_dir) if root_dir else Path.cwd()
        self.output_dir = Path(output_dir)
        self.threads = max(1, min(threads, 32))  # 限制在1-32线程之间
        self.enable_prediction = enable_prediction
        self.skip_prediction = skip_prediction
        self.cache_dir = Path(cache_dir) if cache_dir else None
        self.ncbi_email = ncbi_email
        self.ncbi_api_key = ncbi_api_key
        
        # 初始化NCBI下载器
        if NCBI_DOWNLOADER_AVAILABLE and self.ncbi_email:
            self.ncbi_downloader = NCBIDownloader(email=self.ncbi_email, api_key=self.ncbi_api_key)
        else:
            self.ncbi_downloader = None
            
        self.stats = defaultdict(self._default_stats)
        self.prediction_tools = self.detect_prediction_tools()
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True, parents=True)
        (self.output_dir / 'CDS').mkdir(exist_ok=True)
        (self.output_dir / 'tRNA').mkdir(exist_ok=True)
        (self.output_dir / 'rRNA').mkdir(exist_ok=True)
        
        # 创建缓存目录
        if self.cache_dir:
            self.cache_dir.mkdir(exist_ok=True, parents=True)
        
        logger.info(f"初始化特征提取器: 线程数={self.threads}, 预测={self.enable_prediction}, "
                   f"跳过预测={self.skip_prediction}, 缓存目录={self.cache_dir}")
    
    def _default_stats(self):
        """默认统计值"""
        return {'CDS': 0, 'tRNA': 0, 'rRNA': 0}
    
    def detect_prediction_tools(self) -> Dict[str, bool]:
        """检测系统中可用的预测工具"""
        tools = {
            'prodigal': False,
            'augustus': False,
            'trnascan': False,
            'barrnap': False,
            'prokka': False
        }
        
        try:
            # 检查Prodigal
            subprocess.run(['prodigal', '-v'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            tools['prodigal'] = True
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass
        
        try:
            # 检查Augustus
            subprocess.run(['augustus', '--version'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            tools['augustus'] = True
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass
        
        try:
            # 检查tRNAscan-SE
            subprocess.run(['tRNAscan-SE', '-h'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            tools['trnascan'] = True
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass
        
        try:
            # 检查Barrnap
            subprocess.run(['barrnap', '--version'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            tools['barrnap'] = True
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass
        
        try:
            # 检查Prokka
            subprocess.run(['prokka', '--version'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            tools['prokka'] = True
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass
        
        logger.info(f"检测到预测工具: {', '.join([k for k, v in tools.items() if v])}")
        return tools
    
    def load_metadata(self, metadata_file: str) -> List[Dict[str, str]]:
        """
        加载元数据文件
        
        Args:
            metadata_file: 包含genome_id和kingdom的CSV/TSV文件
            
        Returns:
            list: 包含元数据的字典列表
        """
        metadata = []
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                # 尝试检测分隔符
                first_line = f.readline().strip()
                delimiter = '\t' if '\t' in first_line else ','
                f.seek(0)
                
                reader = csv.DictReader(f, delimiter=delimiter)
                required_fields = ['genome_id', 'kingdom']
                
                # 检查必需字段是否存在
                if not all(field in reader.fieldnames for field in required_fields):
                    missing = [f for f in required_fields if f not in reader.fieldnames]
                    raise ValueError(f"元数据文件缺少必需字段: {missing}")
                
                for row_num, row in enumerate(reader, start=2):  # 从第2行开始计数（第1行是标题）
                    genome_id = row.get('genome_id', '').strip()
                    kingdom = row.get('kingdom', '').strip()
                    
                    # 检查必需字段是否为空
                    if not genome_id or not kingdom:
                        logger.warning(f"第 {row_num} 行数据缺少genome_id或kingdom，跳过此行")
                        continue
                    
                    # 规范化kingdom名称
                    kingdom = kingdom.capitalize()
                    if kingdom not in KINGDOM_DIR_MAPPING:
                        logger.warning(f"第 {row_num} 行: 未知的kingdom '{kingdom}'，使用默认处理")
                    
                    metadata.append({
                        'genome_id': genome_id,
                        'kingdom': kingdom
                    })
            
            if not metadata:
                raise ValueError("元数据文件为空")
                
            logger.info(f"成功加载 {len(metadata)} 个基因组元数据")
            return metadata
        except FileNotFoundError:
            logger.error(f"元数据文件未找到: {metadata_file}")
            raise
        except ValueError as e:
            logger.error(f"元数据文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载元数据文件失败: {e}")
            raise
    
    def find_file(self, directory: Path, patterns: List[str], genome_id: str = None) -> Optional[Path]:
        """
        在目录中查找匹配指定模式的文件
        
        Args:
            directory: 搜索目录
            patterns: 文件模式列表
            genome_id: 基因组ID（用于优先匹配）
            
        Returns:
            Path: 找到的文件路径，如果未找到则返回None
        """
        # 优先查找包含genome_id的文件
        if genome_id:
            for pattern in patterns:
                for file in directory.glob(f"*{genome_id}*{pattern}"):
                    if file.is_file():
                        return file
        
        # 查找所有匹配模式的文件
        for pattern in patterns:
            for file in directory.glob(f"*{pattern}"):
                if file.is_file():
                    return file
        
        # 在子目录中查找
        for pattern in patterns:
            for file in directory.glob(f"**/*{pattern}"):
                if file.is_file():
                    return file
        
        return None
    
    def find_sequence_file(self, genome_path: Path, genome_id: str) -> Path:
        """
        查找基因组序列文件
        
        Args:
            genome_path: 基因组目录路径
            genome_id: 基因组ID
            
        Returns:
            Path: 序列文件路径
            
        Raises:
            FileNotFoundError: 如果未找到序列文件
        """
        patterns = SEQUENCE_EXTENSIONS
        file = self.find_file(genome_path, patterns, genome_id)
        
        if file:
            logger.debug(f"找到序列文件: {file}")
            return file
        
        # 如果在本地未找到，尝试从NCBI下载
        logger.info(f"在本地未找到序列文件，尝试从NCBI下载: {genome_id}")
        downloaded_file = self.download_from_ncbi(genome_id, genome_path)
        if downloaded_file:
            return downloaded_file
        
        raise FileNotFoundError(f"在 {genome_path} 中未找到序列文件，且从NCBI下载失败")
    
    def download_from_ncbi(self, genome_id: str, genome_path: Path) -> Optional[Path]:
        """
        优化的NCBI基因组序列下载器

        Args:
            genome_id: 基因组ID
            genome_path: 基因组目录路径

        Returns:
            Path: 下载的文件路径，如果下载失败则返回None
        """
        try:
            # 如果有NCBI下载器，优先使用
            if self.ncbi_downloader:
                logger.info(f"使用NCBI下载器下载基因组序列: {genome_id}")
                kingdom = self._infer_kingdom_from_path(genome_path)
                downloaded_file = self.ncbi_downloader.download_genome_sequence(
                    genome_id, self.root_dir / "ncbi_downloads", kingdom
                )
                if downloaded_file and downloaded_file.exists():
                    logger.info(f"成功使用NCBI下载器下载序列文件: {downloaded_file}")
                    return downloaded_file

            # 使用内置方法下载
            return self._download_with_builtin_method(genome_id, genome_path)

        except Exception as e:
            logger.error(f"从NCBI下载基因组序列失败: {genome_id} - {e}")
            return None

    def _infer_kingdom_from_path(self, genome_path: Path) -> str:
        """从路径推断kingdom"""
        if genome_path.name in KINGDOM_DIR_MAPPING.values():
            return genome_path.name
        # 从父目录推断
        for part in genome_path.parts:
            if part in KINGDOM_DIR_MAPPING.values():
                return part
        return "Unknown"

    def _download_with_builtin_method(self, genome_id: str, genome_path: Path) -> Optional[Path]:
        """使用内置方法下载基因组序列"""
        logger.info(f"使用内置方法下载基因组序列: {genome_id}")

        # 验证基因组ID格式
        if not self._validate_genome_id(genome_id):
            return None

        # 创建下载目录
        download_dir = self.root_dir / "ncbi_downloads"
        download_dir.mkdir(exist_ok=True, parents=True)

        # 构造FTP URL
        ftp_urls = self._construct_ftp_urls(genome_id)

        # 尝试下载
        for base_url in ftp_urls:
            downloaded_file = self._try_download_from_url(genome_id, base_url, download_dir)
            if downloaded_file:
                # 记录下载信息
                kingdom = self._infer_kingdom_from_path(genome_path)
                self.record_download_info(genome_id, kingdom)
                return downloaded_file

        logger.warning(f"未能从NCBI下载任何序列文件: {genome_id}")
        return None

    def _validate_genome_id(self, genome_id: str) -> bool:
        """验证基因组ID格式"""
        if not genome_id.startswith(('GCF_', 'GCA_')):
            logger.warning(f"基因组ID格式不正确: {genome_id}")
            return False

        parts = genome_id.split('_')
        if len(parts) < 2:
            logger.warning(f"基因组ID格式不正确: {genome_id}")
            return False

        return True

    def _construct_ftp_urls(self, genome_id: str) -> List[str]:
        """构造可能的FTP URL列表"""
        parts = genome_id.split('_')
        accession_prefix = parts[0]  # GCF or GCA
        numeric_part = parts[1].split('.')[0]  # 移除版本号

        # 确保numeric_part至少有9位数字
        numeric_part = numeric_part.ljust(9, '0')
        path_parts = [numeric_part[i:i+3] for i in range(0, 9, 3)]

        # 构造多个可能的URL
        base_urls = []

        # 标准URL
        standard_url = (f"ftp://ftp.ncbi.nlm.nih.gov/genomes/all/"
                       f"{accession_prefix}/{path_parts[0]}/{path_parts[1]}/{path_parts[2]}/{genome_id}")
        base_urls.append(standard_url)

        # 备用URL（不同的服务器）
        alt_url = (f"https://ftp.ncbi.nlm.nih.gov/genomes/all/"
                  f"{accession_prefix}/{path_parts[0]}/{path_parts[1]}/{path_parts[2]}/{genome_id}")
        base_urls.append(alt_url)

        return base_urls

    def _try_download_from_url(self, genome_id: str, base_url: str, download_dir: Path) -> Optional[Path]:
        """尝试从指定URL下载文件"""
        # 可能的文件名
        possible_filenames = [
            f"{genome_id}_genomic.fna.gz",
            f"{genome_id}_genomic.fa.gz",
            f"{genome_id}.fna.gz",
            f"{genome_id}_genomic.fna",
            f"{genome_id}_genomic.fa",
            f"{genome_id}.fna"
        ]

        for filename in possible_filenames:
            url = f"{base_url}/{filename}"
            local_file = download_dir / filename

            try:
                logger.debug(f"尝试下载: {url}")

                # 设置超时和重试
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'Mozilla/5.0 (compatible; GenomeExtractor/1.0)')

                with urllib.request.urlopen(request, timeout=300) as response:
                    with open(local_file, 'wb') as f:
                        shutil.copyfileobj(response, f)

                # 验证下载的文件
                if local_file.exists() and local_file.stat().st_size > 1000:  # 至少1KB
                    logger.info(f"成功下载序列文件: {local_file}")
                    return local_file
                else:
                    logger.warning(f"下载的文件太小或为空: {local_file}")
                    if local_file.exists():
                        local_file.unlink()

            except (urllib.error.URLError, urllib.error.HTTPError) as e:
                logger.debug(f"下载失败 {url}: {e}")
                if local_file.exists():
                    local_file.unlink()
            except Exception as e:
                logger.warning(f"下载过程中出错 {url}: {e}")
                if local_file.exists():
                    local_file.unlink()

        return None
    
    def record_download_info(self, genome_id: str, kingdom: str):
        """
        记录下载信息到CSV文件
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
        """
        try:
            download_log = self.root_dir / "ncbi_downloads" / "download_log.csv"
            
            # 如果文件不存在，创建并写入标题
            if not download_log.exists():
                with open(download_log, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['genome_id', 'kingdom', 'download_time'])
            
            # 追加记录
            with open(download_log, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([genome_id, kingdom, time.strftime('%Y-%m-%d %H:%M:%S')])
                
            logger.info(f"记录下载信息: {genome_id}, {kingdom}")
        except Exception as e:
            logger.error(f"记录下载信息失败: {e}")
    
    def find_annotation_file(self, genome_path: Path, genome_id: str) -> Optional[Path]:
        """
        查找基因组注释文件
        
        Args:
            genome_path: 基因组目录路径
            genome_id: 基因组ID
            
        Returns:
            Path: 注释文件路径，如果未找到则返回None
        """
        patterns = ANNOTATION_EXTENSIONS
        file = self.find_file(genome_path, patterns, genome_id)
        
        if file:
            logger.debug(f"找到注释文件: {file}")
            return file
        
        logger.debug(f"在 {genome_path} 中未找到注释文件")
        return None
    
    def load_genome_sequence(self, seq_file: Path) -> Dict[str, str]:
        """
        优化的基因组序列加载器，支持大文件和内存映射

        Args:
            seq_file: 序列文件路径

        Returns:
            dict: 包含序列ID和序列的字典
        """
        sequences = {}
        file_size = 0

        try:
            file_size = seq_file.stat().st_size
            logger.debug(f"加载序列文件: {seq_file} (大小: {file_size / (1024*1024):.1f} MB)")

            # 处理gzip压缩文件
            if seq_file.suffix == '.gz':
                with gzip.open(seq_file, 'rt', encoding='utf-8', errors='ignore') as handle:
                    sequences = self._parse_sequences_from_handle(handle, seq_file)
            else:
                # 根据文件大小选择处理策略
                if file_size > 200 * 1024 * 1024:  # 200MB以上使用优化的内存映射
                    sequences = self._load_large_sequence_file(seq_file)
                else:
                    # 小文件直接读取
                    with open(seq_file, 'r', encoding='utf-8', errors='ignore') as handle:
                        sequences = self._parse_sequences_from_handle(handle, seq_file)

            if not sequences:
                raise ValueError(f"序列文件为空或无法解析: {seq_file}")

            logger.info(f"成功加载序列文件 {seq_file}，包含 {len(sequences)} 条序列")
            return sequences

        except Exception as e:
            logger.error(f"加载序列文件 {seq_file} 失败 (大小: {file_size / (1024*1024):.1f} MB): {e}")
            raise

    def _parse_sequences_from_handle(self, handle, seq_file: Path) -> Dict[str, str]:
        """从文件句柄解析序列"""
        sequences = {}
        try:
            for i, record in enumerate(SeqIO.parse(handle, 'fasta')):
                # 规范化序列ID
                seq_id = self._normalize_sequence_id(record.id)
                sequences[seq_id] = str(record.seq).upper()

                # 定期报告进度（大文件）
                if (i + 1) % 1000 == 0:
                    logger.debug(f"已解析 {i + 1} 条序列")

        except Exception as e:
            logger.warning(f"解析序列时出现错误: {e}")
            # 尝试继续解析其他序列

        return sequences

    def _load_large_sequence_file(self, seq_file: Path) -> Dict[str, str]:
        """使用内存映射加载大型序列文件"""
        sequences = {}
        try:
            with open(seq_file, 'r', encoding='utf-8', errors='ignore') as f:
                # 创建内存映射
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mapped:
                    # 分块读取以避免内存溢出
                    chunk_size = 50 * 1024 * 1024  # 50MB chunks
                    current_pos = 0
                    buffer = ""

                    while current_pos < len(mapped):
                        # 读取chunk
                        end_pos = min(current_pos + chunk_size, len(mapped))
                        chunk = mapped[current_pos:end_pos].decode('utf-8', errors='ignore')

                        # 处理跨chunk的序列
                        buffer += chunk

                        # 找到完整的序列记录
                        records = buffer.split('>')
                        buffer = '>' + records[-1]  # 保留最后一个可能不完整的记录

                        # 处理完整的记录
                        for record_text in records[1:-1]:  # 跳过第一个空记录和最后一个不完整记录
                            if record_text.strip():
                                lines = record_text.strip().split('\n')
                                if lines:
                                    seq_id = self._normalize_sequence_id(lines[0].split()[0])
                                    sequence = ''.join(lines[1:]).replace(' ', '').replace('\t', '').upper()
                                    if sequence:
                                        sequences[seq_id] = sequence

                        current_pos = end_pos

                    # 处理最后一个记录
                    if buffer.strip() and len(buffer) > 1:
                        record_text = buffer[1:]  # 移除开头的'>'
                        lines = record_text.strip().split('\n')
                        if lines:
                            seq_id = self._normalize_sequence_id(lines[0].split()[0])
                            sequence = ''.join(lines[1:]).replace(' ', '').replace('\t', '').upper()
                            if sequence:
                                sequences[seq_id] = sequence

        except Exception as e:
            logger.error(f"内存映射加载失败: {e}")
            # 回退到普通方法
            with open(seq_file, 'r', encoding='utf-8', errors='ignore') as handle:
                sequences = self._parse_sequences_from_handle(handle, seq_file)

        return sequences

    def _normalize_sequence_id(self, seq_id: str) -> str:
        """规范化序列ID，移除版本号和多余信息"""
        # 移除版本号 (如 NC_000001.11 -> NC_000001)
        if '.' in seq_id and seq_id.count('.') == 1:
            base_id, version = seq_id.split('.')
            if version.isdigit():
                seq_id = base_id

        # 移除管道符号后的内容 (如 gi|123|ref|NC_000001| -> NC_000001)
        if '|' in seq_id:
            parts = seq_id.split('|')
            for part in parts:
                if part.startswith(('NC_', 'NZ_', 'NT_', 'NW_', 'AC_', 'CM_', 'CP_')):
                    seq_id = part
                    break

        return seq_id

    def parse_gff_annotation(self, annotation_file: Path) -> Dict[str, List[Dict]]:
        """
        解析GFF格式注释文件
        
        Args:
            annotation_file: GFF注释文件路径
            
        Returns:
            dict: 包含特征信息的字典
        """
        features = defaultdict(list)
        try:
            # 打开文件（支持gzip压缩）
            if annotation_file.suffix == '.gz':
                file_handle = gzip.open(annotation_file, 'rt')
            else:
                file_handle = open(annotation_file, 'r')
            
            with file_handle as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    seq_id = parts[0]
                    feature_type = parts[2]
                    try:
                        start = int(parts[3]) - 1  # 转换为0基索引
                        end = int(parts[4])
                    except ValueError:
                        logger.warning(f"无效的位置信息: {line.strip()}")
                        continue
                    
                    strand = parts[6]
                    attributes = parts[8]
                    
                    # 提取特征ID和父级ID
                    feature_id = None
                    parent_id = None
                    for attr in attributes.split(';'):
                        attr = attr.strip()
                        if not attr:
                            continue
                        if '=' in attr:
                            key, value = attr.split('=', 1)
                            if key == 'ID':
                                feature_id = value
                            elif key == 'Parent':
                                parent_id = value
                    
                    # 根据特征类型分类
                    if feature_type in ['CDS', 'tRNA', 'rRNA']:
                        features[feature_type].append({
                            'seq_id': seq_id,
                            'start': start,
                            'end': end,
                            'strand': strand,
                            'feature_id': feature_id,
                            'parent_id': parent_id,
                            'attributes': attributes
                        })
            
            logger.info(f"成功解析GFF注释文件 {annotation_file}，找到 {sum(len(v) for v in features.values())} 个特征")
            return features
        except Exception as e:
            logger.error(f"解析GFF注释文件 {annotation_file} 失败: {e}")
            return defaultdict(list)
    
    def parse_gtf_annotation(self, annotation_file: Path) -> Dict[str, List[Dict]]:
        """
        解析GTF格式注释文件
        
        Args:
            annotation_file: GTF注释文件路径
            
        Returns:
            dict: 包含特征信息的字典
        """
        features = defaultdict(list)
        try:
            # 打开文件（支持gzip压缩）
            if annotation_file.suffix == '.gz':
                file_handle = gzip.open(annotation_file, 'rt')
            else:
                file_handle = open(annotation_file, 'r')
            
            with file_handle as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    seq_id = parts[0]
                    feature_type = parts[2]
                    try:
                        start = int(parts[3]) - 1  # 转换为0基索引
                        end = int(parts[4])
                    except ValueError:
                        logger.warning(f"无效的位置信息: {line.strip()}")
                        continue
                    
                    strand = parts[6]
                    attributes = parts[8]
                    
                    # 提取特征ID
                    gene_id = None
                    transcript_id = None
                    
                    for attr in attributes.split(';'):
                        attr = attr.strip()
                        if not attr:
                            continue
                        if ' ' in attr:
                            key, value = attr.split(' ', 1)
                            value = value.strip('"')
                            if key == 'gene_id':
                                gene_id = value
                            elif key == 'transcript_id':
                                transcript_id = value
                    
                    # 根据特征类型分类
                    if feature_type in ['CDS', 'tRNA', 'rRNA']:
                        features[feature_type].append({
                            'seq_id': seq_id,
                            'start': start,
                            'end': end,
                            'strand': strand,
                            'feature_id': transcript_id or gene_id,
                            'gene_id': gene_id,
                            'transcript_id': transcript_id,
                            'attributes': attributes
                        })
            
            logger.info(f"成功解析GTF注释文件 {annotation_file}，找到 {sum(len(v) for v in features.values())} 个特征")
            return features
        except Exception as e:
            logger.error(f"解析GTF注释文件 {annotation_file} 失败: {e}")
            return defaultdict(list)
    
    def parse_genbank_annotation(self, annotation_file: Path) -> Dict[str, List[Dict]]:
        """
        解析GenBank格式注释文件
        
        Args:
            annotation_file: GenBank注释文件路径
            
        Returns:
            dict: 包含特征信息的字典
        """
        features = defaultdict(list)
        try:
            # 打开文件（支持gzip压缩）
            if annotation_file.suffix == '.gz':
                file_handle = gzip.open(annotation_file, 'rt')
            else:
                file_handle = open(annotation_file, 'r')
            
            current_seq_id = None
            current_features = []
            in_feature = False
            current_feature = None
            
            for line in file_handle:
                if line.startswith('LOCUS'):
                    # 解析序列ID
                    parts = line.split()
                    if len(parts) > 1:
                        current_seq_id = parts[1]
                elif line.startswith('FEATURES'):
                    # 特征部分开始
                    current_features = []
                elif line.startswith('     ') and current_seq_id:
                    # 特征行
                    if not in_feature:
                        # 新特征开始
                        feature_type = line[5:12].strip()
                        if feature_type in ['CDS', 'tRNA', 'rRNA']:
                            in_feature = True
                            current_feature = {
                                'seq_id': current_seq_id,
                                'feature_type': feature_type,
                                'start': None,
                                'end': None,
                                'strand': '+',
                                'attributes': []
                            }
                    else:
                        # 特征属性
                        if line[21] != '/':
                            # 多行属性
                            current_feature['attributes'][-1] += ' ' + line[21:].strip()
                        else:
                            # 新属性
                            attr = line[21:].strip()
                            current_feature['attributes'].append(attr)
                elif in_feature and line.startswith('ORIGIN'):
                    # 序列部分开始，结束当前特征
                    if current_feature:
                        current_features.append(current_feature)
                    in_feature = False
                    current_feature = None
                elif in_feature and not line.startswith(' '):
                    # 特征结束
                    if current_feature:
                        # 解析位置信息
                        loc_str = current_feature.get('location', '')
                        if 'complement' in loc_str:
                            current_feature['strand'] = '-'
                            loc_str = loc_str.replace('complement(', '').replace(')', '')
                        if 'join' in loc_str:
                            loc_str = loc_str.replace('join(', '').replace(')', '')
                        
                        # 解析位置范围
                        if '..' in loc_str:
                            start, end = loc_str.split('..')[:2]
                            try:
                                current_feature['start'] = int(start.replace('<', '').replace('>', '')) - 1
                                current_feature['end'] = int(end.replace('<', '').replace('>', ''))
                            except ValueError:
                                logger.warning(f"无效的位置信息: {loc_str}")
                        
                        # 添加到特征列表
                        features[current_feature['feature_type']].append({
                            'seq_id': current_feature['seq_id'],
                            'start': current_feature['start'],
                            'end': current_feature['end'],
                            'strand': current_feature['strand'],
                            'attributes': ';'.join(current_feature['attributes'])
                        })
                    
                    in_feature = False
                    current_feature = None
            
            logger.info(f"成功解析GenBank注释文件 {annotation_file}，找到 {sum(len(v) for v in features.values())} 个特征")
            return features
        except Exception as e:
            logger.error(f"解析GenBank注释文件 {annotation_file} 失败: {e}")
            return defaultdict(list)
    
    def parse_annotation(self, annotation_file: Optional[Path]) -> Dict[str, List[Dict]]:
        """
        根据文件扩展名解析注释文件
        
        Args:
            annotation_file: 注释文件路径
            
        Returns:
            dict: 包含特征信息的字典
        """
        if not annotation_file:
            return defaultdict(list)
        
        if annotation_file.suffix in ['.gff', '.gff3'] or '.gff' in annotation_file.name:
            return self.parse_gff_annotation(annotation_file)
        elif annotation_file.suffix in ['.gtf'] or '.gtf' in annotation_file.name:
            return self.parse_gtf_annotation(annotation_file)
        elif annotation_file.suffix in ['.gbff', '.gbk'] or '.gb' in annotation_file.name:
            return self.parse_genbank_annotation(annotation_file)
        else:
            logger.warning(f"未知的注释文件格式: {annotation_file}")
            return defaultdict(list)
    
    def extract_sequence(self, sequence: str, feature: Dict) -> Optional[str]:
        """
        提取单个特征序列
        
        Args:
            sequence: 完整的序列
            feature: 特征信息
            
        Returns:
            str: 提取的序列，如果失败则返回None
        """
        try:
            start = feature['start']
            end = feature['end']
            
            # 验证位置
            if start < 0 or end > len(sequence) or start >= end:
                logger.warning(f"特征位置无效: {start}-{end} (序列长度: {len(sequence)})")
                return None
            
            # 提取序列
            seq = sequence[start:end]
            
            # 处理负链
            if feature.get('strand', '+') == '-':
                seq = str(Seq(seq).reverse_complement())
            
            return seq
        except Exception as e:
            logger.error(f"提取序列失败: {e}")
            return None
    
    def extract_sequences_parallel(self, genome_id: str, sequences: Dict[str, str], features: Dict[str, List[Dict]], genome_path: Path) -> Dict[str, List[SeqRecord]]:
        """
        并行提取特征序列
        
        Args:
            genome_id: 基因组ID
            sequences: 基因组序列字典
            features: 特征信息字典
            genome_path: 基因组目录路径
            
        Returns:
            dict: 包含提取序列的字典
        """
        extracted = defaultdict(list)
        total_features = sum(len(f) for f in features.values())
        
        if total_features == 0:
            return extracted
        
        # 创建序列ID映射，处理CDS片段的情况
        seq_id_mapping = {}
        for seq_id in sequences.keys():
            # 如果序列ID是CDS片段格式 (如 lcl|NC_006908.1_cds_WP_011264521.1_1)
            if '_cds_' in seq_id:
                # 提取基础序列ID (如 NC_006908.1)
                # 处理 lcl|NC_006908.1_cds_WP_011264521.1_1 格式
                if seq_id.startswith('lcl|'):
                    # 对于 lcl|NC_006908.1_cds_WP_011264521.1_1 格式，提取 NC_006908.1 部分
                    base_id = seq_id.split('_cds_')[0].replace('lcl|', '')
                    seq_id_mapping[base_id] = seq_id
                else:
                    parts = seq_id.split('_cds_')
                    if len(parts) > 0:
                        base_id = parts[0]
                        seq_id_mapping[base_id] = seq_id
            # 也映射原始ID
            seq_id_mapping[seq_id] = seq_id
        
        # 创建反向映射，从基础ID到实际序列ID
        reverse_mapping = {}
        for base_id, actual_id in seq_id_mapping.items():
            if base_id != actual_id:
                reverse_mapping[base_id] = actual_id
        
        # 特殊处理：对于NCBI受限数据，创建从注释文件中的序列ID到实际序列ID的映射
        # 例如：NC_006908.1 -> lcl|NC_006908.1_cds_WP_011264521.1_1
        annotation_to_sequence_mapping = {}
        for feature_type, feature_list in features.items():
            for feature in feature_list:
                seq_id = feature['seq_id']
                # 如果注释文件中的序列ID在我们的映射中存在，建立映射关系
                if seq_id in seq_id_mapping:
                    actual_seq_id = seq_id_mapping[seq_id]
                    annotation_to_sequence_mapping[seq_id] = actual_seq_id
                # 特别处理：如果注释文件中的序列ID是基础ID，而实际序列ID是CDS片段
                elif seq_id in reverse_mapping:
                    actual_seq_id = reverse_mapping[seq_id]
                    annotation_to_sequence_mapping[seq_id] = actual_seq_id
        
        # 进一步处理：对于lcl|格式的CDS片段，创建更完整的映射
        # 例如：NC_006908.1 -> lcl|NC_006908.1_cds_WP_011264521.1_1
        # 我们需要为每个序列创建从注释ID到实际序列ID的映射
        extended_mapping = {}
        for seq_id in sequences.keys():
            if seq_id.startswith('lcl|') and '_cds_' in seq_id:
                # 从 lcl|NC_006908.1_cds_WP_011264521.1_1 提取 NC_006908.1
                base_id = seq_id.split('_cds_')[0].replace('lcl|', '')
                extended_mapping[base_id] = seq_id

        # 创建一个更全面的映射，将注释文件中的序列ID映射到实际序列ID
        # 这个映射将用于在提取序列时找到正确的序列
        comprehensive_mapping = {}
        # 首先添加直接映射
        for seq_id in sequences.keys():
            comprehensive_mapping[seq_id] = seq_id

        # 然后添加扩展映射
        comprehensive_mapping.update(extended_mapping)

        # 最后添加反向映射
        comprehensive_mapping.update(reverse_mapping)

        # 调试信息
        logger.debug(f"extended_mapping: {extended_mapping}")
        logger.debug(f"comprehensive_mapping: {comprehensive_mapping}")
        
        # 记录缺失的序列ID
        missing_seq_ids = set()
        
        # 准备任务列表
        tasks = []
        for feature_type, feature_list in features.items():
            for feature in feature_list:
                seq_id = feature['seq_id']
                # 尝试直接匹配
                if seq_id in sequences:
                    tasks.append((feature_type, feature, sequences[seq_id]))
                # 尝试通过映射匹配
                elif seq_id in seq_id_mapping and seq_id_mapping[seq_id] in sequences:
                    actual_seq_id = seq_id_mapping[seq_id]
                    tasks.append((feature_type, feature, sequences[actual_seq_id]))
                # 尝试通过扩展映射匹配
                elif seq_id in extended_mapping and extended_mapping[seq_id] in sequences:
                    actual_seq_id = extended_mapping[seq_id]
                    tasks.append((feature_type, feature, sequences[actual_seq_id]))
                # 尝试通过综合映射匹配
                elif seq_id in comprehensive_mapping and comprehensive_mapping[seq_id] in sequences:
                    actual_seq_id = comprehensive_mapping[seq_id]
                    tasks.append((feature_type, feature, sequences[actual_seq_id]))
                # 特殊处理：如果注释文件中的序列ID在反向映射中存在
                elif seq_id in reverse_mapping and reverse_mapping[seq_id] in sequences:
                    actual_seq_id = reverse_mapping[seq_id]
                    tasks.append((feature_type, feature, sequences[actual_seq_id]))
                else:
                    logger.warning(f"序列ID {seq_id} 在序列文件中不存在")
                    missing_seq_ids.add(seq_id)
        
        # 检查是否是数据限制问题（NCBI只提供了CDS片段而非完整基因组）
        # 如果所有序列都很短且包含CDS片段特征，很可能是数据限制问题
        if sequences:
            avg_seq_length = sum(len(seq) for seq in sequences.values()) / len(sequences)
            contains_cds_fragments = any("_cds_" in seq_id for seq_id in sequences.keys())
            all_short_sequences = avg_seq_length < 2000  # 如果平均长度小于2000bp
            
            if contains_cds_fragments and all_short_sequences:
                logger.warning("检测到可能的NCBI数据访问限制：序列文件仅包含CDS片段而非完整基因组序列")
                logger.warning("这会导致注释文件中的基因组位置无法匹配到短的CDS片段序列")
                logger.warning("要解决此问题，请确保能够访问完整的基因组序列文件")
        
        # 如果有缺失的序列ID，尝试从NCBI下载
        if missing_seq_ids:
            logger.info(f"发现 {len(missing_seq_ids)} 个缺失的序列ID，尝试从NCBI下载完整的基因组序列文件")
            download_success = self.download_missing_sequences(genome_id, genome_path, missing_seq_ids, sequences)
            
            # 重新检查任务列表，看是否可以处理之前缺失的序列
            tasks = []
            for feature_type, feature_list in features.items():
                for feature in feature_list:
                    seq_id = feature['seq_id']
                    # 尝试直接匹配
                    if seq_id in sequences:
                        tasks.append((feature_type, feature, sequences[seq_id]))
                    # 尝试通过映射匹配
                    elif seq_id in seq_id_mapping and seq_id_mapping[seq_id] in sequences:
                        actual_seq_id = seq_id_mapping[seq_id]
                        tasks.append((feature_type, feature, sequences[actual_seq_id]))
                    else:
                        logger.warning(f"仍然无法找到序列ID {seq_id}，跳过此特征")
        
        # 如果下载成功，重新记录缺失的序列ID
        if missing_seq_ids and download_success:
            # 重新检查缺失的序列ID
            remaining_missing = set()
            for feature_type, feature_list in features.items():
                for feature in feature_list:
                    seq_id = feature['seq_id']
                    if seq_id not in sequences and not (seq_id in seq_id_mapping and seq_id_mapping[seq_id] in sequences):
                        remaining_missing.add(seq_id)
            
            if remaining_missing:
                logger.warning(f"下载后仍有 {len(remaining_missing)} 个序列ID缺失")
            else:
                logger.info("所有序列ID都已成功加载")
        
        # 并行处理
        with ThreadPoolExecutor(max_workers=min(self.threads, 8)) as executor:
            futures = []
            for feature_type, feature, seq in tasks:
                futures.append(executor.submit(self.extract_sequence, seq, feature))
            
            # 收集结果
            for i, future in enumerate(as_completed(futures)):
                feature_type, feature, _ = tasks[i]
                seq_str = future.result()
                if seq_str:
                    # 创建序列记录
                    feature_id = feature.get('feature_id') or feature.get('gene_id') or f"{feature['start']}_{feature['end']}"
                    record_id = f"{genome_id}_{feature_type}_{feature_id}"
                    
                    record = SeqRecord(
                        Seq(seq_str),
                        id=record_id,
                        description=f"{feature_type} from {genome_id} {feature['seq_id']}:{feature['start']+1}-{feature['end']} strand:{feature.get('strand', '+')}"
                    )
                    
                    extracted[feature_type].append(record)
                    self.stats[genome_id][feature_type] += 1
                
                # 定期记录进度
                if (i + 1) % 1000 == 0 or (i + 1) == len(tasks):
                    logger.info(f"提取进度: {i+1}/{len(tasks)} 特征")
        
        return extracted
    
    def predict_cds_prodigal(self, genome_id: str, kingdom: str, sequences: Dict[str, str], temp_dir: Path) -> List[Dict]:
        """
        使用Prodigal预测CDS（适用于原核生物）
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            temp_dir: 临时目录路径
            
        Returns:
            list: 包含预测CDS特征的列表
        """
        if not self.prediction_tools.get('prodigal', False):
            logger.debug("Prodigal不可用，跳过预测")
            return []
            
        if kingdom not in ['Bacteria', 'Archaea']:
            logger.debug(f"跳过Prodigal预测，{kingdom}不是原核生物")
            return []
            
        features = []
        total_length = sum(len(seq) for seq in sequences.values())
        
        # 如果序列太短，Prodigal可能无法处理
        if total_length < 2000:
            logger.debug(f"基因组序列太短({total_length}bp)，跳过Prodigal预测")
            return features
            
        try:
            # 创建输入文件
            input_file = temp_dir / f"{genome_id}_genomic.fasta"
            with open(input_file, 'w') as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")
            
            # 运行Prodigal
            output_gff = temp_dir / f"{genome_id}_prodigal.gff"
            cmd = [
                'prodigal',
                '-i', str(input_file),
                '-o', str(output_gff),
                '-f', 'gff',
                '-p', 'meta' if total_length < 100000 else 'single',
                '-q'  # 静默模式
            ]
            
            logger.info(f"运行Prodigal: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                   text=True, timeout=max(300, total_length // 10000))
            elapsed = time.time() - start_time
            
            if result.returncode != 0:
                logger.warning(f"Prodigal预测失败 (耗时: {elapsed:.1f}s): {result.stderr}")
                return features
                
            logger.info(f"Prodigal成功完成 (耗时: {elapsed:.1f}s)")
            
            # 解析Prodigal输出
            features = []
            with open(output_gff, 'r') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    parts = line.strip().split('\t')
                    if len(parts) < 9 or parts[2] != 'CDS':
                        continue
                        
                    seq_id = parts[0]
                    try:
                        start = int(parts[3]) - 1
                        end = int(parts[4])
                    except ValueError:
                        continue
                    strand = parts[6]
                    attributes = parts[8]
                    
                    feature_id = None
                    for attr in attributes.split(';'):
                        if attr.startswith('ID='):
                            feature_id = attr[3:]
                            break
                    
                    features.append({
                        'seq_id': seq_id,
                        'start': start,
                        'end': end,
                        'strand': strand,
                        'feature_id': feature_id,
                        'attributes': attributes
                    })
                        
            logger.info(f"Prodigal预测到 {len(features)} 个CDS")
            return features
            
        except subprocess.TimeoutExpired:
            logger.warning("Prodigal运行超时")
            return features
        except Exception as e:
            logger.error(f"Prodigal预测出错: {e}")
            return features
    
    def predict_cds_augustus(self, genome_id: str, kingdom: str, sequences: Dict[str, str], temp_dir: Path) -> List[Dict]:
        """
        使用Augustus预测CDS（适用于真核生物）
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            temp_dir: 临时目录路径
            
        Returns:
            list: 包含预测CDS特征的列表
        """
        if not self.prediction_tools.get('augustus', False):
            logger.debug("Augustus不可用，跳过预测")
            return []
            
        if kingdom not in ['Eukaryota', 'Fungi', 'Algae', 'Plant', 'Protist']:
            logger.debug(f"跳过Augustus预测，{kingdom}不是真核生物")
            return []
            
        features = []
        total_length = sum(len(seq) for seq in sequences.values())
        
        # 如果序列太短，Augustus可能无法处理
        if total_length < 1000:
            logger.debug(f"基因组序列太短({total_length}bp)，跳过Augustus预测")
            return features
            
        try:
            # 创建输入文件
            input_file = temp_dir / f"{genome_id}_genomic.fasta"
            with open(input_file, 'w') as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")
            
            # 确定物种参数
            species_map = {
                'Fungi': 'aspergillus_fumigatus',
                'Algae': 'chlamydomonas',
                'Plant': 'arabidopsis',
                'Protist': 'toxoplasma_gondii',
                'Eukaryota': 'human'
            }
            species = species_map.get(kingdom, 'human')
            
            # 运行Augustus
            output_gff = temp_dir / f"{genome_id}_augustus.gff"
            cmd = [
                'augustus',
                '--species', species,
                '--gff3=on',
                '--quiet',
                str(input_file)
            ]
            
            logger.info(f"运行Augustus: {' '.join(cmd)}")
            
            start_time = time.time()
            with open(output_gff, 'w') as outfile:
                result = subprocess.run(cmd, stdout=outfile, stderr=subprocess.PIPE, 
                                      text=True, timeout=max(600, total_length // 1000))
            elapsed = time.time() - start_time
            
            if result.returncode != 0:
                logger.warning(f"Augustus预测失败 (耗时: {elapsed:.1f}s): {result.stderr}")
                return features
                
            logger.info(f"Augustus成功完成 (耗时: {elapsed:.1f}s)")
                
            # 解析Augustus输出
            features = []
            with open(output_gff, 'r') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    parts = line.strip().split('\t')
                    if len(parts) < 9 or parts[2] != 'CDS':
                        continue
                        
                    seq_id = parts[0]
                    try:
                        start = int(parts[3]) - 1
                        end = int(parts[4])
                    except ValueError:
                        continue
                    strand = parts[6]
                    attributes = parts[8]
                    
                    feature_id = None
                    for attr in attributes.split(';'):
                        if attr.startswith('ID='):
                            feature_id = attr[3:]
                            break
                    
                    features.append({
                        'seq_id': seq_id,
                        'start': start,
                        'end': end,
                        'strand': strand,
                        'feature_id': feature_id,
                        'attributes': attributes
                    })
                    
            logger.info(f"Augustus预测到 {len(features)} 个CDS")
            return features
            
        except subprocess.TimeoutExpired:
            logger.warning("Augustus运行超时")
            return features
        except Exception as e:
            logger.error(f"Augustus预测出错: {e}")
            return features
    
    def predict_trna_trnascan(self, genome_id: str, kingdom: str, sequences: Dict[str, str], temp_dir: Path) -> List[Dict]:
        """
        使用tRNAscan-SE预测tRNA
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            temp_dir: 临时目录路径
            
        Returns:
            list: 包含预测tRNA特征的列表
        """
        if not self.prediction_tools.get('trnascan', False):
            logger.debug("tRNAscan-SE不可用，跳过预测")
            return []
            
        features = []
        
        try:
            # 创建输入文件
            input_file = temp_dir / f"{genome_id}_genomic.fasta"
            with open(input_file, 'w') as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")
            
            # 确定模式参数
            mode_map = {
                'Bacteria': '-B',
                'Archaea': '-A',
                'default': '-E'
            }
            mode = mode_map.get(kingdom, '-E')
            
            # 运行tRNAscan-SE
            output_file = temp_dir / f"{genome_id}_trnascan.out"
            cmd = [
                'tRNAscan-SE',
                mode,
                '-o', str(output_file),
                '-Q',  # 静默模式
                str(input_file)
            ]
            
            logger.info(f"运行tRNAscan-SE: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                  text=True, timeout=600)
            elapsed = time.time() - start_time
            
            if result.returncode != 0:
                logger.warning(f"tRNAscan-SE预测失败 (耗时: {elapsed:.1f}s): {result.stderr}")
                return features
                
            logger.info(f"tRNAscan-SE成功完成 (耗时: {elapsed:.1f}s)")
                
            # 解析tRNAscan-SE输出
            if output_file.exists():
                with open(output_file, 'r') as f:
                    # 跳过标题行
                    for _ in range(3):
                        next(f)
                    for line in f:
                        parts = line.strip().split()
                        if len(parts) < 5:
                            continue
                            
                        seq_id = parts[0]
                        try:
                            start = int(parts[2]) - 1
                            end = int(parts[3])
                        except ValueError:
                            continue
                        strand = '+' if parts[4] == 'c' else '-'
                        
                        features.append({
                            'seq_id': seq_id,
                            'start': start,
                            'end': end,
                            'strand': strand,
                            'feature_type': 'tRNA'
                        })
                        
            logger.info(f"tRNAscan-SE预测到 {len(features)} 个tRNA")
            return features
            
        except subprocess.TimeoutExpired:
            logger.warning("tRNAscan-SE运行超时")
            return features
        except Exception as e:
            logger.error(f"tRNAscan-SE预测出错: {e}")
            return features
    
    def predict_rrna_barrnap(self, genome_id: str, kingdom: str, sequences: Dict[str, str], temp_dir: Path) -> List[Dict]:
        """
        使用Barrnap预测rRNA
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            temp_dir: 临时目录路径
            
        Returns:
            list: 包含预测rRNA特征的列表
        """
        if not self.prediction_tools.get('barrnap', False):
            logger.debug("Barrnap不可用，跳过预测")
            return []
            
        features = []
        
        try:
            # 创建输入文件
            input_file = temp_dir / f"{genome_id}_genomic.fasta"
            with open(input_file, 'w') as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")
            
            # 确定王国参数
            kingdom_map = {
                'Bacteria': 'bac',
                'Archaea': 'arc',
                'default': 'euk'
            }
            kingdom_param = kingdom_map.get(kingdom, 'euk')
            
            # 运行Barrnap
            output_file = temp_dir / f"{genome_id}_barrnap.gff"
            cmd = [
                'barrnap',
                '--kingdom', kingdom_param,
                '--outseq', '/dev/null',  # 不输出序列
                '--threads', str(min(self.threads, 4)),
                '--quiet',
                str(input_file)
            ]
            
            logger.info(f"运行Barrnap: {' '.join(cmd)}")
            
            start_time = time.time()
            with open(output_file, 'w') as outfile:
                result = subprocess.run(cmd, stdout=outfile, stderr=subprocess.PIPE, 
                                      text=True, timeout=300)
            elapsed = time.time() - start_time
            
            if result.returncode != 0:
                logger.warning(f"Barrnap预测失败 (耗时: {elapsed:.1f}s): {result.stderr}")
                return features
                
            logger.info(f"Barrnap成功完成 (耗时: {elapsed:.1f}s)")
                
            # 解析Barrnap输出
            with open(output_file, 'r') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    parts = line.strip().split('\t')
                    if len(parts) < 9 or parts[2] != 'rRNA':
                        continue
                        
                    seq_id = parts[0]
                    try:
                        start = int(parts[3]) - 1
                        end = int(parts[4])
                    except ValueError:
                        continue
                    strand = parts[6]
                    attributes = parts[8]
                    
                    feature_id = None
                    for attr in attributes.split(';'):
                        if attr.startswith('Name='):
                            feature_id = attr[5:]
                            break
                    
                    features.append({
                        'seq_id': seq_id,
                        'start': start,
                        'end': end,
                        'strand': strand,
                        'feature_id': feature_id,
                        'attributes': attributes
                    })
                
            logger.info(f"Barrnap预测到 {len(features)} 个rRNA")
            return features
            
        except subprocess.TimeoutExpired:
            logger.warning("Barrnap运行超时")
            return features
        except Exception as e:
            logger.error(f"Barrnap预测出错: {e}")
            return features
    
    def predict_features_with_prokka(self, genome_id: str, kingdom: str, sequences: Dict[str, str], temp_dir: Path) -> Dict[str, List[Dict]]:
        """
        使用Prokka进行综合特征预测（当其他工具无法处理时）
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            temp_dir: 临时目录路径
            
        Returns:
            dict: 包含预测特征的字典
        """
        if not self.prediction_tools.get('prokka', False):
            logger.debug("Prokka不可用，跳过预测")
            return defaultdict(list)
            
        features = defaultdict(list)
        
        try:
            # 创建输入文件
            input_file = temp_dir / f"{genome_id}_genomic.fasta"
            with open(input_file, 'w') as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")
            
            # 确定王国参数
            kingdom_map = {
                'Bacteria': 'Bacteria',
                'Archaea': 'Archaea',
                'default': 'Eukaryota'
            }
            kingdom_param = kingdom_map.get(kingdom, 'Bacteria')
            
            # 创建输出目录
            prokka_output_dir = temp_dir / f"{genome_id}_prokka"
            prokka_output_dir.mkdir(exist_ok=True)
            
            # 运行Prokka
            cmd = [
                'prokka',
                '--outdir', str(prokka_output_dir),
                '--prefix', genome_id,
                '--kingdom', kingdom_param,
                '--force',
                '--quiet',
                '--cpus', str(min(self.threads, 8)),
                str(input_file)
            ]
            
            logger.info(f"运行Prokka: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.PIPE, 
                                  text=True, timeout=1800)  # 30分钟超时
            elapsed = time.time() - start_time
            
            if result.returncode != 0:
                logger.warning(f"Prokka预测失败 (耗时: {elapsed:.1f}s): {result.stderr}")
                return features
                
            logger.info(f"Prokka成功完成 (耗时: {elapsed:.1f}s)")
                
            # 解析Prokka输出的GFF文件
            gff_file = prokka_output_dir / f"{genome_id}.gff"
            if gff_file.exists():
                parsed_features = self.parse_gff_annotation(gff_file)
                for ft in ['CDS', 'tRNA', 'rRNA']:
                    features[ft] = parsed_features.get(ft, [])
            
            logger.info(f"Prokka预测到 CDS={len(features['CDS'])}, tRNA={len(features['tRNA'])}, rRNA={len(features['rRNA'])}")
            return features
            
        except subprocess.TimeoutExpired:
            logger.warning("Prokka运行超时")
            return features
        except Exception as e:
            logger.error(f"Prokka预测出错: {e}")
            return features
    
    def predict_features(self, genome_id: str, kingdom: str, sequences: Dict[str, str],
                        feature_type: str, temp_dir: Path) -> List[Dict]:
        """
        优化的特征预测器，支持多工具回退和缓存

        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            sequences: 基因组序列字典
            feature_type: 特征类型 (CDS, tRNA, rRNA)
            temp_dir: 临时目录路径

        Returns:
            list: 预测的特征列表
        """
        if not self.enable_prediction:
            logger.debug("特征预测已禁用")
            return []

        # 检查序列是否为空
        if not sequences:
            logger.warning(f"序列为空，无法预测 {feature_type} 特征")
            return []

        # 检查缓存
        features = self._load_prediction_cache(genome_id, feature_type)
        if features is not None:
            return features

        # 获取可用的预测工具列表（按优先级排序）
        available_tools = self._get_available_prediction_tools(feature_type, kingdom)

        if not available_tools:
            logger.warning(f"没有可用的预测工具用于 {feature_type} (kingdom: {kingdom})")
            return []

        # 尝试使用预测工具
        features = []
        for tool in available_tools:
            try:
                logger.info(f"使用 {tool} 预测 {feature_type} 特征...")
                features = self._run_prediction_tool(tool, genome_id, kingdom, sequences, feature_type, temp_dir)

                if features:
                    logger.info(f"成功使用 {tool} 预测到 {len(features)} 个 {feature_type} 特征")
                    break
                else:
                    logger.warning(f"{tool} 未预测到任何 {feature_type} 特征")

            except Exception as e:
                logger.error(f"使用 {tool} 预测 {feature_type} 失败: {e}")
                continue

        # 保存到缓存
        self._save_prediction_cache(genome_id, feature_type, features)

        return features

    def _load_prediction_cache(self, genome_id: str, feature_type: str) -> Optional[List[Dict]]:
        """加载预测缓存"""
        if not self.cache_dir:
            return None

        cache_file = self.cache_dir / f"{genome_id}_{feature_type}_prediction.json"
        if not cache_file.exists():
            return None

        try:
            with open(cache_file, 'r') as f:
                features = json.load(f)
            logger.info(f"从缓存加载预测的 {feature_type} 特征: {len(features)} 个")
            return features
        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            return None

    def _save_prediction_cache(self, genome_id: str, feature_type: str, features: List[Dict]):
        """保存预测缓存"""
        if not self.cache_dir or not features:
            return

        cache_file = self.cache_dir / f"{genome_id}_{feature_type}_prediction.json"
        try:
            with open(cache_file, 'w') as f:
                json.dump(features, f, indent=2)
            logger.debug(f"预测结果保存到缓存: {cache_file}")
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _get_available_prediction_tools(self, feature_type: str, kingdom: str) -> List[str]:
        """获取可用的预测工具列表（按优先级排序）"""
        tool_mapping = PREDICTION_TOOL_MAPPING.get(feature_type, {})

        # 优先使用kingdom特定的工具
        preferred_tool = tool_mapping.get(kingdom)
        default_tool = tool_mapping.get('default')

        # 构建工具优先级列表
        tools_to_try = []

        if preferred_tool and self.prediction_tools.get(preferred_tool, False):
            tools_to_try.append(preferred_tool)

        if default_tool and default_tool != preferred_tool and self.prediction_tools.get(default_tool, False):
            tools_to_try.append(default_tool)

        # 添加其他可用工具作为备选
        for tool_name, available in self.prediction_tools.items():
            if available and tool_name not in tools_to_try:
                # 检查工具是否适用于该特征类型
                if self._is_tool_suitable_for_feature(tool_name, feature_type):
                    tools_to_try.append(tool_name)

        return tools_to_try

    def _is_tool_suitable_for_feature(self, tool_name: str, feature_type: str) -> bool:
        """检查工具是否适用于指定特征类型"""
        tool_feature_map = {
            'prodigal': ['CDS'],
            'augustus': ['CDS'],
            'trnascan': ['tRNA'],
            'barrnap': ['rRNA'],
            'prokka': ['CDS', 'tRNA', 'rRNA']  # Prokka可以预测所有类型
        }

        return feature_type in tool_feature_map.get(tool_name, [])

    def _run_prediction_tool(self, tool: str, genome_id: str, kingdom: str,
                           sequences: Dict[str, str], feature_type: str, temp_dir: Path) -> List[Dict]:
        """运行指定的预测工具"""
        if tool == 'prodigal':
            return self.predict_cds_prodigal(genome_id, kingdom, sequences, temp_dir)
        elif tool == 'augustus':
            return self.predict_cds_augustus(genome_id, kingdom, sequences, temp_dir)
        elif tool == 'trnascan':
            return self.predict_trna_trnascan(genome_id, kingdom, sequences, temp_dir)
        elif tool == 'barrnap':
            return self.predict_rrna_barrnap(genome_id, kingdom, sequences, temp_dir)
        elif tool == 'prokka':
            prokka_features = self.predict_features_with_prokka(genome_id, kingdom, sequences, temp_dir)
            return prokka_features.get(feature_type, [])
        else:
            logger.error(f"未知的预测工具: {tool}")
            return []
    
    def process_genome(self, metadata_row: Dict[str, str]) -> Dict[str, Any]:
        """
        处理单个基因组
        
        Args:
            metadata_row: 包含genome_id和kingdom的字典
            
        Returns:
            dict: 处理结果统计
        """
        genome_id = metadata_row['genome_id']
        kingdom = metadata_row['kingdom']
        logger.info(f"开始处理基因组: {genome_id} ({kingdom})")
        
        try:
            # 获取基因组目录
            kingdom_dir = KINGDOM_DIR_MAPPING.get(kingdom, kingdom)
            genome_path = self.root_dir / kingdom_dir / genome_id
            
            # 如果基因组目录不存在，尝试从NCBI下载
            if not genome_path.exists():
                logger.info(f"基因组目录不存在，尝试从NCBI下载: {genome_path}")
                downloaded_file = self.download_from_ncbi(genome_id, genome_path)
                if not downloaded_file:
                    raise FileNotFoundError(f"基因组目录不存在且从NCBI下载失败: {genome_path}")
                # 如果下载成功，使用下载的文件
                seq_file = downloaded_file
            else:
                # 查找序列文件
                seq_file = self.find_sequence_file(genome_path, genome_id)
            
            # 加载基因组序列
            sequences = self.load_genome_sequence(seq_file)
            
            # 创建临时目录用于预测工具
            with tempfile.TemporaryDirectory(prefix=f"genome_{genome_id}_") as temp_dir:
                temp_path = Path(temp_dir)
                
                # 查找并解析注释文件
                annotation_file = self.find_annotation_file(genome_path, genome_id)
                parsed_features = self.parse_annotation(annotation_file) if annotation_file else defaultdict(list)
                logger.info(f"解析到特征: CDS={len(parsed_features['CDS'])}, tRNA={len(parsed_features['tRNA'])}, rRNA={len(parsed_features['rRNA'])}")
                
                # 检查是否需要预测缺失的特征
                if self.skip_prediction:
                    logger.info("跳过特征预测")
                    features = parsed_features
                else:
                    # 预测缺失的特征类型
                    features = defaultdict(list)
                    for feature_type in ['CDS', 'tRNA', 'rRNA']:
                        # 优先使用已解析的特征
                        if parsed_features[feature_type]:
                            features[feature_type] = parsed_features[feature_type]
                            logger.info(f"使用已解析的 {feature_type} 特征: {len(features[feature_type])} 个")
                        else:
                            # 预测缺失的特征
                            logger.info(f"预测 {feature_type} 特征...")
                            predicted = self.predict_features(genome_id, kingdom, sequences, feature_type, temp_path)
                            features[feature_type] = predicted
                            logger.info(f"预测到 {feature_type} 特征: {len(predicted)} 个")
                
                # 提取序列
                extracted_sequences = self.extract_sequences_parallel(genome_id, sequences, features, genome_path)
                
                # 保存序列
                self.save_sequences(genome_id, extracted_sequences)
            
            logger.info(f"完成处理基因组 {genome_id}: CDS={self.stats[genome_id]['CDS']}, "
                      f"tRNA={self.stats[genome_id]['tRNA']}, rRNA={self.stats[genome_id]['rRNA']}")
            
            return {
                'genome_id': genome_id,
                'status': 'success',
                'CDS_count': self.stats[genome_id]['CDS'],
                'tRNA_count': self.stats[genome_id]['tRNA'],
                'rRNA_count': self.stats[genome_id]['rRNA']
            }
            
        except Exception as e:
            logger.error(f"处理基因组 {genome_id} 失败: {str(e)}", exc_info=True)
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': str(e)
            }
    
    def save_sequences(self, genome_id: str, extracted_sequences: Dict[str, List[SeqRecord]]):
        """
        保存提取的序列到文件
        
        Args:
            genome_id: 基因组ID
            extracted_sequences: 提取的序列字典
        """
        for feature_type, seq_records in extracted_sequences.items():
            if not seq_records:
                logger.info(f"没有 {feature_type} 序列需要保存")
                continue
                
            output_file = self.output_dir / feature_type / f"{genome_id}_{feature_type}.fasta.gz"
            try:
                with gzip.open(output_file, 'wt') as f:
                    SeqIO.write(seq_records, f, 'fasta')
                logger.info(f"保存 {len(seq_records)} 个 {feature_type} 序列到 {output_file}")
            except Exception as e:
                logger.error(f"保存 {feature_type} 序列失败: {e}")
    
    def process_genomes(self, metadata: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        优化的多线程基因组处理器

        Args:
            metadata: 元数据列表

        Returns:
            list: 处理结果列表
        """
        total_genomes = len(metadata)
        logger.info(f"开始处理 {total_genomes} 个基因组，使用 {self.threads} 个线程")

        results = []
        processed = 0
        start_time = time.time()

        # 动态调整线程数
        optimal_threads = self._calculate_optimal_threads(total_genomes)
        logger.info(f"使用优化的线程数: {optimal_threads}")

        # 批量处理以减少内存压力
        batch_size = max(1, min(50, total_genomes // optimal_threads))

        for batch_start in range(0, total_genomes, batch_size):
            batch_end = min(batch_start + batch_size, total_genomes)
            batch_metadata = metadata[batch_start:batch_end]

            logger.info(f"处理批次 {batch_start//batch_size + 1}/{(total_genomes + batch_size - 1)//batch_size}: "
                       f"基因组 {batch_start + 1}-{batch_end}")

            batch_results = self._process_genome_batch(batch_metadata, optimal_threads)
            results.extend(batch_results)

            processed += len(batch_results)

            # 显示总体进度
            elapsed = time.time() - start_time
            progress = processed / total_genomes
            eta = (elapsed / processed) * (total_genomes - processed) if processed > 0 else 0

            successful = sum(1 for r in batch_results if r['status'] == 'success')
            failed = len(batch_results) - successful

            logger.info(f"批次完成: 成功 {successful}, 失败 {failed}")
            logger.info(f"总进度: {processed}/{total_genomes} ({progress*100:.1f}%) - "
                       f"用时: {elapsed:.1f}s, ETA: {eta:.1f}s")

            # 内存清理
            if batch_start > 0 and batch_start % (batch_size * 5) == 0:
                logger.debug("执行内存清理...")
                import gc
                gc.collect()

        return results

    def _calculate_optimal_threads(self, total_genomes: int) -> int:
        """计算最优线程数"""
        # 基于系统资源和任务数量动态调整
        cpu_count = multiprocessing.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)

        # 基础线程数：不超过CPU核心数的75%
        base_threads = max(1, int(cpu_count * 0.75))

        # 根据内存调整：每个线程大约需要1-2GB内存
        memory_threads = max(1, int(memory_gb / 2))

        # 根据任务数量调整
        task_threads = min(total_genomes, 8)  # 最多8个线程

        # 取最小值作为最优线程数
        optimal = min(self.threads, base_threads, memory_threads, task_threads)

        logger.debug(f"线程数计算: CPU={cpu_count}, 内存={memory_gb:.1f}GB, "
                    f"基础={base_threads}, 内存限制={memory_threads}, "
                    f"任务限制={task_threads}, 最终={optimal}")

        return optimal

    def _process_genome_batch(self, batch_metadata: List[Dict[str, str]],
                             max_workers: int) -> List[Dict[str, Any]]:
        """处理一批基因组"""
        batch_results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_metadata = {
                executor.submit(self._safe_process_genome, row): row
                for row in batch_metadata
            }

            # 收集结果
            for future in as_completed(future_to_metadata):
                metadata_row = future_to_metadata[future]
                try:
                    result = future.result(timeout=3600)  # 1小时超时
                    batch_results.append(result)

                    # 简化的进度显示
                    status = "✓" if result['status'] == 'success' else "✗"
                    logger.info(f"{status} {metadata_row['genome_id']}")

                except Exception as e:
                    logger.error(f"处理基因组失败: {metadata_row['genome_id']} - {e}")
                    batch_results.append({
                        'genome_id': metadata_row['genome_id'],
                        'status': 'error',
                        'error': str(e)[:200],  # 限制错误信息长度
                        'CDS_count': 0,
                        'tRNA_count': 0,
                        'rRNA_count': 0
                    })

        return batch_results

    def _safe_process_genome(self, metadata_row: Dict[str, str]) -> Dict[str, Any]:
        """安全的基因组处理包装器"""
        try:
            return self.process_genome(metadata_row)
        except Exception as e:
            logger.error(f"基因组处理异常: {metadata_row['genome_id']} - {e}")
            return {
                'genome_id': metadata_row['genome_id'],
                'status': 'error',
                'error': str(e)[:200],
                'CDS_count': 0,
                'tRNA_count': 0,
                'rRNA_count': 0
            }
    
    def save_statistics(self, results: List[Dict[str, Any]], output_file: str = 'feature_statistics.csv'):
        """
        保存统计结果到CSV文件
        
        Args:
            results: 处理结果列表
            output_file: 输出文件名
        """
        try:
            stats_file = self.output_dir / output_file
            with open(stats_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error'])
                
                for result in results:
                    if result['status'] == 'success':
                        writer.writerow([
                            result['genome_id'],
                            result['CDS_count'],
                            result['tRNA_count'],
                            result['rRNA_count'],
                            'success',
                            ''
                        ])
                    else:
                        writer.writerow([
                            result['genome_id'],
                            0, 0, 0,
                            'error',
                            result.get('error', 'unknown error')[:100]  # 限制错误信息长度
                        ])
            
            logger.info(f"统计结果已保存到 {stats_file}")
            return stats_file
        except Exception as e:
            logger.error(f"保存统计结果失败: {e}")
            return None
    
    def download_missing_sequences(self, genome_id: str, genome_path: Path, missing_seq_ids: set, sequences: Dict[str, str]):
        """
        从NCBI下载完整的基因组序列文件
        
        Args:
            genome_id: 基因组ID
            genome_path: 基因组目录路径
            missing_seq_ids: 缺失的序列ID集合
            sequences: 序列字典，用于更新下载的序列
        """
        try:
            logger.info(f"发现 {len(missing_seq_ids)} 个缺失的序列ID，尝试从NCBI下载完整的基因组序列文件")
            
            # 检查是否是数据格式问题（只有CDS序列而非完整基因组）
            # 如果大部分序列ID都缺失，或者序列数很少，可能是这种情况
            # 或者如果缺失的序列ID是完整的基因组ID（如NC_006908.1），而现有序列都是CDS片段
            # 或者如果所有序列ID都相同（数据限制问题）
            # 或者如果序列ID包含"_cds_"表示是CDS片段
            unique_seq_ids = set(sequences.keys())
            all_same_id = len(unique_seq_ids) == 1 if unique_seq_ids else False
            contains_cds_fragments = any("_cds_" in seq_id for seq_id in sequences.keys())
            
            # 检查是否包含NCBI受限数据的特征（序列ID包含lcl|前缀且是CDS片段）
            contains_lcl_cds = any(seq_id.startswith('lcl|') and '_cds_' in seq_id for seq_id in sequences.keys())
            
            # 检查序列长度是否都很短（CDS片段通常较短）
            avg_seq_length = sum(len(seq) for seq in sequences.values()) / len(sequences) if sequences else 0
            short_sequences = avg_seq_length < 2000  # 如果平均序列长度小于2000bp，可能是CDS片段
            
            # 检查是否是NCBI数据访问限制问题
            is_ncbi_restriction = (
                len(sequences) < 50 or  # 序列数很少
                all_same_id or  # 所有序列ID都相同
                contains_cds_fragments or  # 包含CDS片段
                contains_lcl_cds or  # 包含lcl|前缀的CDS片段
                short_sequences  # 序列普遍较短
            )
            
            if is_ncbi_restriction:
                logger.warning("检测到可能的NCBI数据访问限制：序列文件仅包含CDS片段而非完整基因组序列")
                logger.warning("这会导致注释文件中的基因组位置无法匹配到短的CDS片段序列")
                logger.warning("要解决此问题，将从NCBI下载完整的基因组序列文件")
                logger.info(f"序列统计: 序列数={len(sequences)}, 唯一ID数={len(unique_seq_ids)}, 所有ID相同={all_same_id}, 包含CDS片段={contains_cds_fragments}, 包含lcl|CDS={contains_lcl_cds}, 平均长度={avg_seq_length:.1f}bp")
                
                # 尝试下载完整的基因组序列文件
                complete_genome_file = self.download_complete_genome_sequence(genome_id, genome_path)
                
                if complete_genome_file and complete_genome_file.exists():
                    logger.info(f"成功下载完整的基因组序列文件: {complete_genome_file}")
                    
                    # 清空现有的序列字典
                    sequences.clear()
                    
                    # 重新加载所有序列
                    loaded_sequences = self.load_genome_sequence(complete_genome_file)
                    sequences.update(loaded_sequences)
                    
                    logger.info(f"重新加载了 {len(sequences)} 条序列")
                    return True
                else:
                    logger.warning(f"无法下载完整的基因组序列文件: {genome_id}")
            
            # 使用现有的download_from_ncbi方法下载完整的基因组序列文件
            downloaded_file = self.download_from_ncbi(genome_id, genome_path)
            
            if downloaded_file and downloaded_file.exists():
                logger.info(f"成功从NCBI下载完整的基因组序列文件: {downloaded_file}")
                
                # 清空现有的序列字典
                sequences.clear()
                
                # 重新加载所有序列
                loaded_sequences = self.load_genome_sequence(downloaded_file)
                sequences.update(loaded_sequences)
                
                logger.info(f"重新加载了 {len(sequences)} 条序列")
                return True
            else:
                logger.warning(f"无法从NCBI下载完整的基因组序列文件: {genome_id}")
                return False
                    
        except Exception as e:
            logger.error(f"下载完整基因组序列时出错: {e}")
            return False
    
    def download_complete_genome_sequence(self, genome_id: str, genome_path: Path) -> Optional[Path]:
        """
        从NCBI下载完整的基因组序列文件（专门处理只有CDS序列的情况）
        
        Args:
            genome_id: 基因组ID
            genome_path: 基因组目录路径
            
        Returns:
            Path: 下载的完整基因组序列文件路径，如果下载失败则返回None
        """
        try:
            # 如果有NCBI下载器，优先使用
            if self.ncbi_downloader:
                logger.info(f"使用NCBI下载器下载完整基因组序列: {genome_id}")
                kingdom = genome_path.name if genome_path.name in KINGDOM_DIR_MAPPING.values() else "Unknown"
                downloaded_file = self.ncbi_downloader.download_genome_sequence(genome_id, self.root_dir / "ncbi_downloads", kingdom)
                if downloaded_file and downloaded_file.exists():
                    logger.info(f"成功使用NCBI下载器下载完整基因组序列文件: {downloaded_file}")
                    return downloaded_file
            
            # 如果没有NCBI下载器或下载失败，使用内置方法
            logger.info(f"使用内置方法下载完整基因组序列: {genome_id}")
            
            # 创建下载目录
            download_dir = self.root_dir / "ncbi_downloads"
            download_dir.mkdir(exist_ok=True)
            
            # 构造FTP URL
            if not genome_id.startswith(('GCF_', 'GCA_')):
                logger.warning(f"基因组ID格式不正确: {genome_id}")
                return None
                
            parts = genome_id.split('_')
            # 修复：对于基因组ID如GCF_900142335.1，parts长度为2，不是3
            if len(parts) < 2:
                logger.warning(f"基因组ID格式不正确: {genome_id}")
                return None
                
            accession_prefix = parts[0]  # GCF or GCA
            numeric_part = parts[1]      # XXXXXXXXX
            
            # 构造路径
            numeric_part = numeric_part.ljust(9, '0')
            path_parts = [numeric_part[i:i+3] for i in range(0, 9, 3)]
            ftp_url_base = f"ftp://ftp.ncbi.nlm.nih.gov/genomes/all/{accession_prefix}/{path_parts[0]}/{path_parts[1]}/{path_parts[2]}/{genome_id}"
            
            # 尝试下载完整的基因组序列文件（优先级高于CDS序列）
            complete_genome_filenames = [
                f"{genome_id}_genomic.fna.gz",
                f"{genome_id}_genomic.fna",
                f"{genome_id}.fna.gz",
                f"{genome_id}.fna"
            ]
            
            downloaded_file = None
            for filename in complete_genome_filenames:
                url = f"{ftp_url_base}/{filename}"
                local_file = download_dir / filename
                
                try:
                    logger.info(f"尝试下载完整基因组序列: {url}")
                    urllib.request.urlretrieve(url, local_file)
                    if local_file.exists() and local_file.stat().st_size > 0:
                        logger.info(f"成功下载完整基因组序列文件: {local_file}")
                        downloaded_file = local_file
                        break
                except urllib.error.URLError as e:
                    logger.warning(f"下载完整基因组序列失败 {url}: {e}")
                    if local_file.exists():
                        local_file.unlink()
                except Exception as e:
                    logger.warning(f"下载完整基因组序列过程中出错 {url}: {e}")
                    if local_file.exists():
                        local_file.unlink()
            
            if downloaded_file:
                # 记录下载信息
                self.record_download_info(genome_id, genome_path.name if genome_path.name in KINGDOM_DIR_MAPPING.values() else "Unknown")
                return downloaded_file
                
            logger.warning(f"未能下载完整的基因组序列文件: {genome_id}")
            return None
            
        except Exception as e:
            logger.error(f"下载完整基因组序列时出错: {e}")
            return None
    
    def generate_summary_report(self, results: List[Dict[str, Any]], report_file: str = 'summary_report.txt'):
        """
        生成处理摘要报告
        
        Args:
            results: 处理结果列表
            report_file: 报告文件名
        """
        try:
            report_path = self.output_dir / report_file
            successful = [r for r in results if r['status'] == 'success']
            failed = [r for r in results if r['status'] == 'error']
            
            total_cds = sum(r['CDS_count'] for r in successful)
            total_trna = sum(r['tRNA_count'] for r in successful)
            total_rrna = sum(r['rRNA_count'] for r in successful)
            
            with open(report_path, 'w') as f:
                f.write("=" * 60 + "\n")
                f.write("基因组特征提取摘要报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总基因组数: {len(results)}\n")
                f.write(f"成功处理: {len(successful)}\n")
                f.write(f"处理失败: {len(failed)}\n\n")
                
                f.write(f"总计提取特征:\n")
                f.write(f"  CDS序列: {total_cds}\n")
                f.write(f"  tRNA序列: {total_trna}\n")
                f.write(f"  rRNA序列: {total_rrna}\n\n")
                
                if failed:
                    f.write("失败基因组:\n")
                    for fail in failed:
                        f.write(f"  - {fail['genome_id']}: {fail.get('error', '未知错误')}\n")
            
            logger.info(f"摘要报告已保存到 {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"生成摘要报告失败: {e}")
            return None
    
    def run(self, metadata_file: str):
        """
        运行基因组特征提取流程
        
        Args:
            metadata_file: 元数据文件路径
        """
        try:
            start_time = time.time()
            
            # 加载元数据
            metadata = self.load_metadata(metadata_file)
            
            # 处理基因组
            results = self.process_genomes(metadata)
            
            # 保存统计结果
            stats_file = self.save_statistics(results)
            
            # 生成摘要报告
            report_file = self.generate_summary_report(results)
            
            # 输出摘要信息
            successful = sum(1 for r in results if r['status'] == 'success')
            failed = len(results) - successful
            total_time = time.time() - start_time
            
            logger.info("=" * 60)
            logger.info("处理完成!")
            logger.info(f"总用时: {total_time:.1f} 秒")
            logger.info(f"成功处理: {successful} 个基因组")
            logger.info(f"处理失败: {failed} 个基因组")
            
            if successful > 0:
                total_cds = sum(r['CDS_count'] for r in results if r['status'] == 'success')
                total_trna = sum(r['tRNA_count'] for r in results if r['status'] == 'success')
                total_rrna = sum(r['rRNA_count'] for r in results if r['status'] == 'success')
                
                logger.info(f"总计提取:")
                logger.info(f"  CDS序列: {total_cds} 个")
                logger.info(f"  tRNA序列: {total_trna} 个")
                logger.info(f"  rRNA序列: {total_rrna} 个")
            
            if stats_file:
                logger.info(f"统计文件: {stats_file}")
            if report_file:
                logger.info(f"摘要报告: {report_file}")
            
            logger.info("=" * 60)
            
            return 0
        except Exception as e:
            logger.error(f"运行基因组特征提取流程失败: {e}", exc_info=True)
            return 1

def memory_usage() -> float:
    """返回当前进程的内存使用量(MB)"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / (1024 * 1024)

def main():
    parser = argparse.ArgumentParser(
        description='基因组特征提取器 - 从基因组序列和注释文件中提取CDS、tRNA和rRNA序列',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('metadata_file', help='包含genome_id和kingdom的元数据文件路径')
    parser.add_argument('--root_dir', default=None, help='基因组数据根目录 (默认: 当前目录)')
    parser.add_argument('--output_dir', default='extracted_features', help='输出目录 (默认: extracted_features)')
    parser.add_argument('--threads', type=int, default=4, help='线程数 (默认: 4)')
    parser.add_argument('--log_level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       help='日志级别 (默认: INFO)')
    parser.add_argument('--disable_prediction', action='store_true', 
                       help='禁用特征预测 (仅使用现有注释)')
    parser.add_argument('--cache_dir', default=None, help='预测结果缓存目录')
    parser.add_argument('--ncbi_email', default=None, help='NCBI下载器邮箱')
    parser.add_argument('--ncbi_api_key', default=None, help='NCBI下载器API密钥')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))
    
    # 记录初始内存使用
    logger.info(f"初始内存使用: {memory_usage():.2f} MB")
    
    # 创建特征提取器并运行
    extractor = GenomeFeatureExtractor(
        root_dir=args.root_dir,
        output_dir=args.output_dir,
        threads=args.threads,
        enable_prediction=not args.disable_prediction,
        cache_dir=args.cache_dir,
        ncbi_email=args.ncbi_email,
        ncbi_api_key=args.ncbi_api_key
    )
    
    exit_code = extractor.run(args.metadata_file)
    
    # 记录最终内存使用
    logger.info(f"最终内存使用: {memory_usage():.2f} MB")
    
    sys.exit(exit_code)

if __name__ == '__main__':
    main()
