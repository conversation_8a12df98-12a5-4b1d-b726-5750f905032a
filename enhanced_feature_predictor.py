#!/usr/bin/env python3
"""
增强版基因组特征预测器
使用多种预测工具处理传统方法无法提取的基因组特征

主要功能：
1. 分析仍然失败的基因组（特征计数为0）
2. 使用多种预测工具：Prodigal、tRNAscan-SE、Barrnap、RNAmmer等
3. 整合在线预测服务和本地工具
4. 生成高质量的预测结果

作者：AI Assistant
日期：2025-08-20
版本：1.0
"""

import os
import sys
import csv
import gzip
import json
import logging
import argparse
import time
import tempfile
import shutil
import subprocess
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_feature_predictor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 生物界分类目录映射
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi', 
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

class EnhancedFeaturePredictor:
    def __init__(self, statistics_file='extracted_features_seq/feature_statistics.csv',
                 output_dir='extracted_features_seq', root_dir=None, threads=4,
                 conda_env='genome_analysis', use_online_tools=True):
        """
        初始化增强版特征预测器
        
        Args:
            statistics_file: 特征统计文件路径
            output_dir: 输出目录
            root_dir: 基因组数据根目录
            threads: 线程数
            conda_env: conda环境名称
            use_online_tools: 是否使用在线预测工具
        """
        self.statistics_file = Path(statistics_file)
        self.output_dir = Path(output_dir)
        self.root_dir = Path(root_dir) if root_dir else Path.cwd()
        self.threads = max(1, min(threads, 8))
        self.conda_env = conda_env
        self.use_online_tools = use_online_tools
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True, parents=True)
        for feature_type in ['CDS', 'tRNA', 'rRNA']:
            (self.output_dir / feature_type).mkdir(exist_ok=True)
        
        # 预测工具配置
        self.prediction_tools = {
            'CDS': ['prodigal', 'genemark', 'glimmer'],
            'tRNA': ['trnascan', 'aragorn'],
            'rRNA': ['barrnap', 'rnammer', 'infernal']
        }
        
        # 统计信息
        self.stats = {
            'total_failed': 0,
            'cds_predicted': 0,
            'trna_predicted': 0,
            'rrna_predicted': 0,
            'still_failed': 0
        }
        
        logger.info(f"初始化增强版特征预测器: 线程数={self.threads}, 在线工具={self.use_online_tools}")
    
    def load_failed_genomes(self) -> List[Dict[str, Any]]:
        """
        加载仍然失败的基因组
        
        Returns:
            list: 失败基因组列表
        """
        failed_genomes = []
        try:
            with open(self.statistics_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    genome_id = row.get('genome_id', '').strip()
                    if not genome_id:
                        continue
                    
                    # 转换数值字段
                    try:
                        cds_count = int(row.get('CDS_count', 0))
                        trna_count = int(row.get('tRNA_count', 0))
                        rrna_count = int(row.get('rRNA_count', 0))
                    except ValueError:
                        cds_count = trna_count = rrna_count = 0
                    
                    # 识别仍然失败的基因组
                    if cds_count == 0 or trna_count == 0 or rrna_count == 0:
                        kingdom = self.infer_kingdom_from_id(genome_id)
                        failed_genomes.append({
                            'genome_id': genome_id,
                            'kingdom': kingdom,
                            'cds_count': cds_count,
                            'trna_count': trna_count,
                            'rrna_count': rrna_count,
                            'needs_cds': cds_count == 0,
                            'needs_trna': trna_count == 0,
                            'needs_rrna': rrna_count == 0
                        })
            
            self.stats['total_failed'] = len(failed_genomes)
            logger.info(f"识别出 {len(failed_genomes)} 个仍然失败的基因组")
            return failed_genomes
        except Exception as e:
            logger.error(f"加载失败基因组列表失败: {e}")
            raise
    
    def infer_kingdom_from_id(self, genome_id: str) -> str:
        """
        从基因组ID推断kingdom分类
        
        Args:
            genome_id: 基因组ID
            
        Returns:
            str: 推断的kingdom
        """
        # 尝试在现有目录结构中查找
        for kingdom in KINGDOM_DIR_MAPPING.values():
            kingdom_dir = self.root_dir / kingdom
            if kingdom_dir.exists():
                genome_dir = kingdom_dir / genome_id
                if genome_dir.exists():
                    return kingdom
        
        # 默认返回Bacteria
        return 'Bacteria'
    
    def find_genome_sequence_file(self, genome_id: str, kingdom: str) -> Optional[Path]:
        """
        查找基因组序列文件
        
        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类
            
        Returns:
            Path: 序列文件路径，如果未找到则返回None
        """
        kingdom_dir = self.root_dir / kingdom / genome_id
        if not kingdom_dir.exists():
            return None
        
        # 查找可能的序列文件
        possible_files = [
            f"{genome_id}_genomic.fna",
            f"{genome_id}_genomic.fna.gz",
            "genomic.fna",
            "genomic.fna.gz",
            f"{genome_id}.fna",
            f"{genome_id}.fna.gz"
        ]
        
        for filename in possible_files:
            seq_file = kingdom_dir / filename
            if seq_file.exists():
                return seq_file
        
        # 查找目录中的任何fna文件
        for seq_file in kingdom_dir.glob("*.fna*"):
            if seq_file.is_file():
                return seq_file
        
        return None
    
    def predict_cds_with_prodigal(self, genome_id: str, seq_file: Path, output_dir: Path) -> int:
        """
        使用Prodigal预测CDS
        
        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录
            
        Returns:
            int: 预测的CDS数量
        """
        try:
            output_file = output_dir / f"{genome_id}_CDS_predicted.fasta"
            temp_gff = output_dir / f"{genome_id}_temp.gff"
            
            # 构造Prodigal命令
            cmd = [
                'prodigal',
                '-i', str(seq_file),
                '-d', str(output_file),
                '-f', 'gff',
                '-o', str(temp_gff),
                '-q'  # 安静模式
            ]
            
            logger.debug(f"运行Prodigal: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_file.exists():
                # 计算预测的CDS数量
                cds_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        cds_count += 1
                
                # 压缩输出文件
                compressed_file = output_dir / f"{genome_id}_CDS.fasta.gz"
                with open(output_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # 清理临时文件
                output_file.unlink()
                if temp_gff.exists():
                    temp_gff.unlink()
                
                logger.info(f"Prodigal预测CDS成功: {genome_id} ({cds_count} 个CDS)")
                return cds_count
            else:
                logger.warning(f"Prodigal预测CDS失败: {genome_id} - {result.stderr}")
                return 0
                
        except subprocess.TimeoutExpired:
            logger.error(f"Prodigal预测CDS超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"Prodigal预测CDS出错: {genome_id} - {e}")
            return 0
    
    def predict_trna_with_trnascan(self, genome_id: str, seq_file: Path, output_dir: Path) -> int:
        """
        使用tRNAscan-SE预测tRNA
        
        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录
            
        Returns:
            int: 预测的tRNA数量
        """
        try:
            output_file = output_dir / f"{genome_id}_tRNA_predicted.fasta"
            temp_out = output_dir / f"{genome_id}_trna_temp.out"
            
            # 构造tRNAscan-SE命令
            cmd = [
                'tRNAscan-SE',
                '-B',  # 细菌模式
                '-f', str(output_file),
                '-o', str(temp_out),
                str(seq_file)
            ]
            
            logger.debug(f"运行tRNAscan-SE: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0 and output_file.exists():
                # 计算预测的tRNA数量
                trna_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        trna_count += 1
                
                # 压缩输出文件
                compressed_file = output_dir / f"{genome_id}_tRNA.fasta.gz"
                with open(output_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # 清理临时文件
                output_file.unlink()
                if temp_out.exists():
                    temp_out.unlink()
                
                logger.info(f"tRNAscan-SE预测tRNA成功: {genome_id} ({trna_count} 个tRNA)")
                return trna_count
            else:
                logger.warning(f"tRNAscan-SE预测tRNA失败: {genome_id} - {result.stderr}")
                return 0
                
        except subprocess.TimeoutExpired:
            logger.error(f"tRNAscan-SE预测tRNA超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"tRNAscan-SE预测tRNA出错: {genome_id} - {e}")
            return 0
    
    def predict_rrna_with_barrnap(self, genome_id: str, seq_file: Path, output_dir: Path, kingdom: str) -> int:
        """
        使用Barrnap预测rRNA
        
        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录
            kingdom: 生物界分类
            
        Returns:
            int: 预测的rRNA数量
        """
        try:
            output_file = output_dir / f"{genome_id}_rRNA_predicted.fasta"
            
            # 根据kingdom选择模型
            kingdom_model = {
                'Bacteria': 'bac',
                'Archaea': 'arc',
                'Eukaryota': 'euk',
                'Fungi': 'euk'
            }.get(kingdom, 'bac')
            
            # 构造Barrnap命令
            cmd = [
                'barrnap',
                '--kingdom', kingdom_model,
                '--outseq', str(output_file),
                str(seq_file)
            ]
            
            logger.debug(f"运行Barrnap: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_file.exists():
                # 计算预测的rRNA数量
                rrna_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        rrna_count += 1
                
                if rrna_count > 0:
                    # 压缩输出文件
                    compressed_file = output_dir / f"{genome_id}_rRNA.fasta.gz"
                    with open(output_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    logger.info(f"Barrnap预测rRNA成功: {genome_id} ({rrna_count} 个rRNA)")
                else:
                    logger.info(f"Barrnap未找到rRNA: {genome_id}")
                
                # 清理临时文件
                output_file.unlink()
                return rrna_count
            else:
                logger.warning(f"Barrnap预测rRNA失败: {genome_id} - {result.stderr}")
                return 0
                
        except subprocess.TimeoutExpired:
            logger.error(f"Barrnap预测rRNA超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"Barrnap预测rRNA出错: {genome_id} - {e}")
            return 0

    def predict_features_for_genome(self, genome_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        为单个基因组预测特征

        Args:
            genome_info: 基因组信息字典

        Returns:
            dict: 预测结果
        """
        genome_id = genome_info['genome_id']
        kingdom = genome_info['kingdom']

        logger.info(f"开始预测基因组特征: {genome_id} ({kingdom})")

        # 查找序列文件
        seq_file = self.find_genome_sequence_file(genome_id, kingdom)
        if not seq_file:
            logger.error(f"未找到基因组序列文件: {genome_id}")
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': 'Sequence file not found',
                'CDS_count': 0,
                'tRNA_count': 0,
                'rRNA_count': 0
            }

        logger.info(f"找到序列文件: {seq_file}")

        # 创建临时工作目录
        temp_dir = Path(tempfile.mkdtemp(prefix=f"predict_{genome_id}_"))

        try:
            # 解压序列文件（如果需要）
            working_seq_file = seq_file
            if seq_file.suffix == '.gz':
                working_seq_file = temp_dir / seq_file.stem
                with gzip.open(seq_file, 'rb') as f_in:
                    with open(working_seq_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

            results = {
                'genome_id': genome_id,
                'status': 'success',
                'CDS_count': genome_info['cds_count'],
                'tRNA_count': genome_info['trna_count'],
                'rRNA_count': genome_info['rrna_count']
            }

            # 预测CDS（如果需要）
            if genome_info['needs_cds']:
                logger.info(f"预测CDS: {genome_id}")
                cds_count = self.predict_cds_with_prodigal(genome_id, working_seq_file, self.output_dir / 'CDS')
                if cds_count > 0:
                    results['CDS_count'] = cds_count
                    self.stats['cds_predicted'] += 1

            # 预测tRNA（如果需要）
            if genome_info['needs_trna']:
                logger.info(f"预测tRNA: {genome_id}")
                trna_count = self.predict_trna_with_trnascan(genome_id, working_seq_file, self.output_dir / 'tRNA')
                if trna_count > 0:
                    results['tRNA_count'] = trna_count
                    self.stats['trna_predicted'] += 1

            # 预测rRNA（如果需要）
            if genome_info['needs_rrna']:
                logger.info(f"预测rRNA: {genome_id}")
                rrna_count = self.predict_rrna_with_barrnap(genome_id, working_seq_file, self.output_dir / 'rRNA', kingdom)
                if rrna_count > 0:
                    results['rRNA_count'] = rrna_count
                    self.stats['rrna_predicted'] += 1

            # 检查是否仍有失败
            if results['CDS_count'] == 0 or results['tRNA_count'] == 0 or results['rRNA_count'] == 0:
                self.stats['still_failed'] += 1

            logger.info(f"预测完成: {genome_id} - CDS={results['CDS_count']}, tRNA={results['tRNA_count']}, rRNA={results['rRNA_count']}")
            return results

        except Exception as e:
            logger.error(f"预测基因组特征失败: {genome_id} - {e}")
            self.stats['still_failed'] += 1
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': str(e),
                'CDS_count': genome_info['cds_count'],
                'tRNA_count': genome_info['trna_count'],
                'rRNA_count': genome_info['rrna_count']
            }
        finally:
            # 清理临时目录
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)

    def batch_predict_features(self, failed_genomes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量预测基因组特征

        Args:
            failed_genomes: 失败基因组列表

        Returns:
            list: 预测结果列表
        """
        logger.info(f"开始批量预测 {len(failed_genomes)} 个基因组的特征")

        results = []
        start_time = time.time()

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            future_to_genome = {}
            for genome_info in failed_genomes:
                future = executor.submit(self.predict_features_for_genome, genome_info)
                future_to_genome[future] = genome_info

            # 收集结果
            completed = 0
            for future in as_completed(future_to_genome):
                genome_info = future_to_genome[future]
                genome_id = genome_info['genome_id']

                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"处理基因组 {genome_id} 时出错: {e}")
                    results.append({
                        'genome_id': genome_id,
                        'status': 'error',
                        'error': str(e),
                        'CDS_count': genome_info['cds_count'],
                        'tRNA_count': genome_info['trna_count'],
                        'rRNA_count': genome_info['rrna_count']
                    })
                    self.stats['still_failed'] += 1

                completed += 1
                elapsed = time.time() - start_time
                progress = completed / len(failed_genomes) * 100
                eta = (elapsed / completed) * (len(failed_genomes) - completed) if completed > 0 else 0

                logger.info(f"进度: {completed}/{len(failed_genomes)} ({progress:.1f}%) - "
                          f"用时: {elapsed:.1f}s, ETA: {eta:.1f}s - "
                          f"CDS预测: {self.stats['cds_predicted']}, "
                          f"tRNA预测: {self.stats['trna_predicted']}, "
                          f"rRNA预测: {self.stats['rrna_predicted']}")

        return results

    def update_statistics_file(self, original_statistics: List[Dict[str, Any]],
                             prediction_results: List[Dict[str, Any]]):
        """
        更新统计文件

        Args:
            original_statistics: 原始统计数据
            prediction_results: 预测结果
        """
        try:
            # 创建结果映射
            result_map = {result['genome_id']: result for result in prediction_results}

            # 更新统计数据
            updated_statistics = []
            for row in original_statistics:
                genome_id = row['genome_id']
                if genome_id in result_map:
                    result = result_map[genome_id]
                    if result['status'] == 'success':
                        # 更新预测结果
                        row['CDS_count'] = result['CDS_count']
                        row['tRNA_count'] = result['tRNA_count']
                        row['rRNA_count'] = result['rRNA_count']
                        row['status'] = 'predicted'
                        row['error'] = ''
                    else:
                        # 更新失败结果
                        row['status'] = 'prediction_failed'
                        row['error'] = result.get('error', 'Prediction failed')[:100]

                updated_statistics.append(row)

            # 备份原文件
            backup_file = self.statistics_file.with_suffix('.csv.prediction_backup')
            shutil.copy2(self.statistics_file, backup_file)
            logger.info(f"原统计文件已备份到: {backup_file}")

            # 写入更新后的统计文件
            with open(self.statistics_file, 'w', newline='', encoding='utf-8') as f:
                if updated_statistics:
                    fieldnames = ['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(updated_statistics)

            logger.info(f"统计文件已更新: {self.statistics_file}")

        except Exception as e:
            logger.error(f"更新统计文件失败: {e}")
            raise

    def generate_prediction_report(self, failed_genomes: List[Dict[str, Any]],
                                 prediction_results: List[Dict[str, Any]]):
        """
        生成预测报告

        Args:
            failed_genomes: 失败基因组列表
            prediction_results: 预测结果
        """
        try:
            report_file = self.output_dir / 'prediction_report.txt'

            successful_predictions = [r for r in prediction_results if r['status'] == 'success']
            failed_predictions = [r for r in prediction_results if r['status'] == 'error']

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("基因组特征预测报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总失败基因组数: {self.stats['total_failed']}\n")
                f.write(f"CDS预测成功: {self.stats['cds_predicted']}\n")
                f.write(f"tRNA预测成功: {self.stats['trna_predicted']}\n")
                f.write(f"rRNA预测成功: {self.stats['rrna_predicted']}\n")
                f.write(f"仍然失败: {self.stats['still_failed']}\n")
                f.write(f"conda环境: {self.conda_env}\n\n")

                if successful_predictions:
                    f.write("成功预测的基因组:\n")
                    f.write("-" * 30 + "\n")
                    for result in successful_predictions:
                        f.write(f"  {result['genome_id']}: "
                               f"CDS={result['CDS_count']}, "
                               f"tRNA={result['tRNA_count']}, "
                               f"rRNA={result['rRNA_count']}\n")
                    f.write("\n")

                if failed_predictions:
                    f.write("预测失败的基因组:\n")
                    f.write("-" * 30 + "\n")
                    for result in failed_predictions:
                        f.write(f"  {result['genome_id']}: {result.get('error', 'Unknown error')}\n")

            logger.info(f"预测报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"生成预测报告失败: {e}")

    def run(self):
        """
        运行增强版特征预测流程
        """
        try:
            start_time = time.time()

            # 加载失败的基因组
            logger.info("加载仍然失败的基因组...")
            failed_genomes = self.load_failed_genomes()

            if not failed_genomes:
                logger.info("没有需要预测的基因组")
                return

            # 批量预测特征
            logger.info("开始批量预测特征...")
            prediction_results = self.batch_predict_features(failed_genomes)

            # 加载原始统计数据
            original_statistics = []
            with open(self.statistics_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                original_statistics = list(reader)

            # 更新统计文件
            logger.info("更新统计文件...")
            self.update_statistics_file(original_statistics, prediction_results)

            # 生成报告
            logger.info("生成预测报告...")
            self.generate_prediction_report(failed_genomes, prediction_results)

            # 输出摘要
            elapsed_time = time.time() - start_time
            logger.info("=" * 60)
            logger.info("特征预测完成!")
            logger.info(f"总用时: {elapsed_time:.1f} 秒")
            logger.info(f"总失败基因组数: {self.stats['total_failed']}")
            logger.info(f"CDS预测成功: {self.stats['cds_predicted']}")
            logger.info(f"tRNA预测成功: {self.stats['trna_predicted']}")
            logger.info(f"rRNA预测成功: {self.stats['rrna_predicted']}")
            logger.info(f"仍然失败: {self.stats['still_failed']}")

            if self.stats['total_failed'] > 0:
                success_rate = (self.stats['total_failed'] - self.stats['still_failed']) / self.stats['total_failed'] * 100
                logger.info(f"改善率: {success_rate:.1f}%")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"运行特征预测流程失败: {e}", exc_info=True)
            raise


def main():
    parser = argparse.ArgumentParser(
        description='增强版基因组特征预测器 - 使用预测工具处理失败的基因组',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--statistics_file', default='extracted_features_seq/feature_statistics.csv',
                       help='特征统计文件路径')
    parser.add_argument('--output_dir', default='extracted_features_seq',
                       help='输出目录')
    parser.add_argument('--root_dir', default=None,
                       help='基因组数据根目录')
    parser.add_argument('--threads', type=int, default=4,
                       help='线程数')
    parser.add_argument('--conda_env', default='genome_analysis',
                       help='conda环境名称')
    parser.add_argument('--disable_online_tools', action='store_true',
                       help='禁用在线预测工具')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))

    # 创建预测器并运行
    predictor = EnhancedFeaturePredictor(
        statistics_file=args.statistics_file,
        output_dir=args.output_dir,
        root_dir=args.root_dir,
        threads=args.threads,
        conda_env=args.conda_env,
        use_online_tools=not args.disable_online_tools
    )

    predictor.run()


if __name__ == '__main__':
    main()
