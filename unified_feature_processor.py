#!/usr/bin/env python3
"""
统一基因组特征处理器
融合特征重新提取和预测工具的完整解决方案

主要功能：
1. 自动识别需要处理的基因组（特征计数为0）
2. 首先尝试传统的重新提取方法
3. 对仍然失败的基因组使用专业预测工具
4. 生成完整的处理报告和统计

预测工具：
- Prodigal: CDS预测
- tRNAscan-SE: tRNA预测  
- Barrnap: rRNA预测

作者：AI Assistant
日期：2025-08-20
版本：2.0 (融合版)
"""

import os
import sys
import csv
import gzip
import json
import logging
import argparse
import time
import tempfile
import shutil
import subprocess
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_feature_processor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 生物界分类目录映射
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi', 
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

class UnifiedFeatureProcessor:
    def __init__(self, statistics_file='extracted_features_seq/feature_statistics.csv',
                 output_dir='extracted_features_seq', root_dir=None, threads=4,
                 conda_env='genome_analysis', enable_prediction=True):
        """
        初始化统一特征处理器
        
        Args:
            statistics_file: 特征统计文件路径
            output_dir: 输出目录
            root_dir: 基因组数据根目录
            threads: 线程数
            conda_env: conda环境名称
            enable_prediction: 是否启用预测工具
        """
        self.statistics_file = Path(statistics_file)
        self.output_dir = Path(output_dir)
        self.root_dir = Path(root_dir) if root_dir else Path.cwd()
        self.threads = max(1, min(threads, 8))
        self.conda_env = conda_env
        self.enable_prediction = enable_prediction
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True, parents=True)
        for feature_type in ['CDS', 'tRNA', 'rRNA']:
            (self.output_dir / feature_type).mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'need_processing': 0,
            'reextraction_success': 0,
            'reextraction_failed': 0,
            'prediction_success': 0,
            'prediction_failed': 0,
            'final_success': 0,
            'still_failed': 0
        }
        
        logger.info(f"初始化统一特征处理器: 线程数={self.threads}, 预测工具={'启用' if self.enable_prediction else '禁用'}")
    
    def load_statistics(self) -> List[Dict[str, Any]]:
        """
        加载特征统计文件
        
        Returns:
            list: 统计数据列表
        """
        statistics = []
        try:
            with open(self.statistics_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 转换数值字段
                    try:
                        row['CDS_count'] = int(row.get('CDS_count', 0))
                        row['tRNA_count'] = int(row.get('tRNA_count', 0))
                        row['rRNA_count'] = int(row.get('rRNA_count', 0))
                    except ValueError:
                        row['CDS_count'] = 0
                        row['tRNA_count'] = 0
                        row['rRNA_count'] = 0
                    
                    statistics.append(row)
            
            self.stats['total_genomes'] = len(statistics)
            logger.info(f"成功加载 {len(statistics)} 个基因组的统计信息")
            return statistics
        except FileNotFoundError:
            logger.error(f"统计文件未找到: {self.statistics_file}")
            raise
        except Exception as e:
            logger.error(f"加载统计文件失败: {e}")
            raise
    
    def identify_genomes_for_processing(self, statistics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别需要处理的基因组
        
        Args:
            statistics: 统计数据列表
            
        Returns:
            list: 需要处理的基因组列表
        """
        genomes_to_process = []
        
        for row in statistics:
            genome_id = row.get('genome_id', '').strip()
            if not genome_id:
                continue
            
            cds_count = row.get('CDS_count', 0)
            trna_count = row.get('tRNA_count', 0)
            rrna_count = row.get('rRNA_count', 0)
            status = row.get('status', '').strip()
            
            # 检查是否需要处理（任何一个为0就需要处理）
            needs_processing = (
                cds_count == 0 or 
                trna_count == 0 or 
                rrna_count == 0 or
                status != 'success'
            )
            
            if needs_processing:
                kingdom = self.infer_kingdom_from_id(genome_id)
                genomes_to_process.append({
                    'genome_id': genome_id,
                    'kingdom': kingdom,
                    'original_cds': cds_count,
                    'original_trna': trna_count,
                    'original_rrna': rrna_count,
                    'original_status': status,
                    'needs_cds': cds_count == 0,
                    'needs_trna': trna_count == 0,
                    'needs_rrna': rrna_count == 0
                })
        
        self.stats['need_processing'] = len(genomes_to_process)
        logger.info(f"识别出 {len(genomes_to_process)} 个需要处理的基因组")
        return genomes_to_process
    
    def infer_kingdom_from_id(self, genome_id: str) -> str:
        """
        从基因组ID推断kingdom分类
        
        Args:
            genome_id: 基因组ID
            
        Returns:
            str: 推断的kingdom
        """
        # 尝试在现有目录结构中查找
        for kingdom in KINGDOM_DIR_MAPPING.values():
            kingdom_dir = self.root_dir / kingdom
            if kingdom_dir.exists():
                genome_dir = kingdom_dir / genome_id
                if genome_dir.exists():
                    return kingdom
        
        # 默认返回Bacteria
        return 'Bacteria'
    
    def create_temp_metadata_file(self, genomes_to_process: List[Dict[str, Any]]) -> Path:
        """
        创建临时元数据文件用于重新提取
        
        Args:
            genomes_to_process: 需要处理的基因组列表
            
        Returns:
            Path: 临时元数据文件路径
        """
        temp_metadata = Path('temp_unified_metadata.csv')
        
        with open(temp_metadata, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['genome_id', 'kingdom'])
            for genome_info in genomes_to_process:
                writer.writerow([genome_info['genome_id'], genome_info['kingdom']])
        
        logger.info(f"创建临时元数据文件: {temp_metadata}")
        return temp_metadata
    
    def run_genome_feature_extractor(self, metadata_file: Path) -> bool:
        """
        运行genome_feature_extractor.py进行重新提取
        
        Args:
            metadata_file: 元数据文件路径
            
        Returns:
            bool: 是否成功运行
        """
        try:
            # 构造命令
            cmd = [
                'conda', 'run', '-n', self.conda_env,
                'python', 'genome_feature_extractor.py',
                str(metadata_file),
                '--output_dir', str(self.output_dir),
                '--threads', str(self.threads),
                '--log_level', 'INFO'
            ]
            
            if self.root_dir != Path.cwd():
                cmd.extend(['--root_dir', str(self.root_dir)])
            
            logger.info(f"第一阶段：运行传统特征提取...")
            logger.debug(f"运行命令: {' '.join(cmd)}")
            
            # 运行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=7200  # 2小时超时
            )
            
            if result.returncode == 0:
                logger.info("传统特征提取运行成功")
                return True
            else:
                logger.error(f"传统特征提取运行失败，返回码: {result.returncode}")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("传统特征提取运行超时")
            return False
        except Exception as e:
            logger.error(f"运行传统特征提取时出错: {e}")
            return False

    def find_genome_sequence_file(self, genome_id: str, kingdom: str) -> Optional[Path]:
        """
        查找基因组序列文件

        Args:
            genome_id: 基因组ID
            kingdom: 生物界分类

        Returns:
            Path: 序列文件路径，如果未找到则返回None
        """
        kingdom_dir = self.root_dir / kingdom / genome_id
        if not kingdom_dir.exists():
            return None

        # 查找可能的序列文件
        possible_files = [
            f"{genome_id}_genomic.fna",
            f"{genome_id}_genomic.fna.gz",
            "genomic.fna",
            "genomic.fna.gz",
            f"{genome_id}.fna",
            f"{genome_id}.fna.gz"
        ]

        for filename in possible_files:
            seq_file = kingdom_dir / filename
            if seq_file.exists():
                return seq_file

        # 查找目录中的任何fna文件
        for seq_file in kingdom_dir.glob("*.fna*"):
            if seq_file.is_file():
                return seq_file

        return None

    def predict_cds_with_prodigal(self, genome_id: str, seq_file: Path, output_dir: Path) -> int:
        """
        使用Prodigal预测CDS

        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录

        Returns:
            int: 预测的CDS数量
        """
        try:
            output_file = output_dir / f"{genome_id}_CDS_predicted.fasta"
            temp_gff = output_dir / f"{genome_id}_temp.gff"

            # 构造Prodigal命令
            cmd = [
                'prodigal',
                '-i', str(seq_file),
                '-d', str(output_file),
                '-f', 'gff',
                '-o', str(temp_gff),
                '-q'  # 安静模式
            ]

            logger.debug(f"运行Prodigal: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0 and output_file.exists():
                # 计算预测的CDS数量
                cds_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        cds_count += 1

                # 压缩输出文件
                compressed_file = output_dir / f"{genome_id}_CDS.fasta.gz"
                with open(output_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

                # 清理临时文件
                output_file.unlink()
                if temp_gff.exists():
                    temp_gff.unlink()

                logger.info(f"Prodigal预测CDS成功: {genome_id} ({cds_count} 个CDS)")
                return cds_count
            else:
                logger.warning(f"Prodigal预测CDS失败: {genome_id}")
                return 0

        except subprocess.TimeoutExpired:
            logger.error(f"Prodigal预测CDS超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"Prodigal预测CDS出错: {genome_id} - {e}")
            return 0

    def predict_trna_with_trnascan(self, genome_id: str, seq_file: Path, output_dir: Path) -> int:
        """
        使用tRNAscan-SE预测tRNA

        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录

        Returns:
            int: 预测的tRNA数量
        """
        try:
            output_file = output_dir / f"{genome_id}_tRNA_predicted.fasta"
            temp_out = output_dir / f"{genome_id}_trna_temp.out"

            # 构造tRNAscan-SE命令
            cmd = [
                'tRNAscan-SE',
                '-B',  # 细菌模式
                '-f', str(output_file),
                '-o', str(temp_out),
                str(seq_file)
            ]

            logger.debug(f"运行tRNAscan-SE: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            if result.returncode == 0 and output_file.exists():
                # 计算预测的tRNA数量
                trna_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        trna_count += 1

                # 压缩输出文件
                compressed_file = output_dir / f"{genome_id}_tRNA.fasta.gz"
                with open(output_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

                # 清理临时文件
                output_file.unlink()
                if temp_out.exists():
                    temp_out.unlink()

                logger.info(f"tRNAscan-SE预测tRNA成功: {genome_id} ({trna_count} 个tRNA)")
                return trna_count
            else:
                logger.warning(f"tRNAscan-SE预测tRNA失败: {genome_id}")
                return 0

        except subprocess.TimeoutExpired:
            logger.error(f"tRNAscan-SE预测tRNA超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"tRNAscan-SE预测tRNA出错: {genome_id} - {e}")
            return 0

    def predict_rrna_with_barrnap(self, genome_id: str, seq_file: Path, output_dir: Path, kingdom: str) -> int:
        """
        使用Barrnap预测rRNA

        Args:
            genome_id: 基因组ID
            seq_file: 序列文件路径
            output_dir: 输出目录
            kingdom: 生物界分类

        Returns:
            int: 预测的rRNA数量
        """
        try:
            output_file = output_dir / f"{genome_id}_rRNA_predicted.fasta"

            # 根据kingdom选择模型
            kingdom_model = {
                'Bacteria': 'bac',
                'Archaea': 'arc',
                'Eukaryota': 'euk',
                'Fungi': 'euk'
            }.get(kingdom, 'bac')

            # 构造Barrnap命令
            cmd = [
                'barrnap',
                '--kingdom', kingdom_model,
                '--outseq', str(output_file),
                str(seq_file)
            ]

            logger.debug(f"运行Barrnap: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0 and output_file.exists():
                # 计算预测的rRNA数量
                rrna_count = 0
                with open(output_file, 'r') as f:
                    for record in SeqIO.parse(f, 'fasta'):
                        rrna_count += 1

                if rrna_count > 0:
                    # 压缩输出文件
                    compressed_file = output_dir / f"{genome_id}_rRNA.fasta.gz"
                    with open(output_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)

                    logger.info(f"Barrnap预测rRNA成功: {genome_id} ({rrna_count} 个rRNA)")
                else:
                    logger.info(f"Barrnap未找到rRNA: {genome_id}")

                # 清理临时文件
                output_file.unlink()
                return rrna_count
            else:
                logger.warning(f"Barrnap预测rRNA失败: {genome_id}")
                return 0

        except subprocess.TimeoutExpired:
            logger.error(f"Barrnap预测rRNA超时: {genome_id}")
            return 0
        except Exception as e:
            logger.error(f"Barrnap预测rRNA出错: {genome_id} - {e}")
            return 0

    def predict_features_for_genome(self, genome_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        为单个基因组预测特征

        Args:
            genome_info: 基因组信息字典

        Returns:
            dict: 预测结果
        """
        genome_id = genome_info['genome_id']
        kingdom = genome_info['kingdom']

        logger.info(f"第二阶段：预测基因组特征: {genome_id} ({kingdom})")

        # 查找序列文件
        seq_file = self.find_genome_sequence_file(genome_id, kingdom)
        if not seq_file:
            logger.error(f"未找到基因组序列文件: {genome_id}")
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': 'Sequence file not found',
                'CDS_count': genome_info['original_cds'],
                'tRNA_count': genome_info['original_trna'],
                'rRNA_count': genome_info['original_rrna']
            }

        # 创建临时工作目录
        temp_dir = Path(tempfile.mkdtemp(prefix=f"predict_{genome_id}_"))

        try:
            # 解压序列文件（如果需要）
            working_seq_file = seq_file
            if seq_file.suffix == '.gz':
                working_seq_file = temp_dir / seq_file.stem
                with gzip.open(seq_file, 'rb') as f_in:
                    with open(working_seq_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

            results = {
                'genome_id': genome_id,
                'status': 'success',
                'CDS_count': genome_info['original_cds'],
                'tRNA_count': genome_info['original_trna'],
                'rRNA_count': genome_info['original_rrna']
            }

            prediction_made = False

            # 预测CDS（如果需要）
            if genome_info['needs_cds']:
                logger.info(f"预测CDS: {genome_id}")
                cds_count = self.predict_cds_with_prodigal(genome_id, working_seq_file, self.output_dir / 'CDS')
                if cds_count > 0:
                    results['CDS_count'] = cds_count
                    prediction_made = True

            # 预测tRNA（如果需要）
            if genome_info['needs_trna']:
                logger.info(f"预测tRNA: {genome_id}")
                trna_count = self.predict_trna_with_trnascan(genome_id, working_seq_file, self.output_dir / 'tRNA')
                if trna_count > 0:
                    results['tRNA_count'] = trna_count
                    prediction_made = True

            # 预测rRNA（如果需要）
            if genome_info['needs_rrna']:
                logger.info(f"预测rRNA: {genome_id}")
                rrna_count = self.predict_rrna_with_barrnap(genome_id, working_seq_file, self.output_dir / 'rRNA', kingdom)
                if rrna_count > 0:
                    results['rRNA_count'] = rrna_count
                    prediction_made = True

            if prediction_made:
                self.stats['prediction_success'] += 1
            else:
                self.stats['prediction_failed'] += 1

            logger.info(f"预测完成: {genome_id} - CDS={results['CDS_count']}, tRNA={results['tRNA_count']}, rRNA={results['rRNA_count']}")
            return results

        except Exception as e:
            logger.error(f"预测基因组特征失败: {genome_id} - {e}")
            self.stats['prediction_failed'] += 1
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': str(e),
                'CDS_count': genome_info['original_cds'],
                'tRNA_count': genome_info['original_trna'],
                'rRNA_count': genome_info['original_rrna']
            }
        finally:
            # 清理临时目录
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)

    def analyze_reextraction_results(self, original_genomes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析重新提取的结果，找出仍然失败的基因组

        Args:
            original_genomes: 原始需要处理的基因组列表

        Returns:
            list: 仍然失败的基因组列表
        """
        # 重新加载统计文件
        new_stats_file = self.output_dir / 'feature_statistics.csv'
        if not new_stats_file.exists():
            logger.warning("未找到重新提取后的统计文件")
            return original_genomes

        # 读取新的统计结果
        new_results = {}
        with open(new_stats_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                genome_id = row['genome_id']
                new_results[genome_id] = {
                    'CDS_count': int(row.get('CDS_count', 0)),
                    'tRNA_count': int(row.get('tRNA_count', 0)),
                    'rRNA_count': int(row.get('rRNA_count', 0)),
                    'status': row.get('status', 'unknown')
                }

        # 分析结果
        still_failed = []
        for genome_info in original_genomes:
            genome_id = genome_info['genome_id']
            if genome_id in new_results:
                new_data = new_results[genome_id]
                # 检查是否仍然失败
                if (new_data['CDS_count'] == 0 or
                    new_data['tRNA_count'] == 0 or
                    new_data['rRNA_count'] == 0):

                    # 更新基因组信息
                    genome_info.update({
                        'original_cds': new_data['CDS_count'],
                        'original_trna': new_data['tRNA_count'],
                        'original_rrna': new_data['rRNA_count'],
                        'needs_cds': new_data['CDS_count'] == 0,
                        'needs_trna': new_data['tRNA_count'] == 0,
                        'needs_rrna': new_data['rRNA_count'] == 0
                    })
                    still_failed.append(genome_info)
                else:
                    self.stats['reextraction_success'] += 1
            else:
                # 如果没有找到结果，认为仍然失败
                still_failed.append(genome_info)

        self.stats['reextraction_failed'] = len(still_failed)
        logger.info(f"重新提取结果: 成功={self.stats['reextraction_success']}, 仍然失败={len(still_failed)}")
        return still_failed

    def batch_predict_features(self, failed_genomes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量预测基因组特征

        Args:
            failed_genomes: 失败基因组列表

        Returns:
            list: 预测结果列表
        """
        if not failed_genomes:
            logger.info("没有需要预测的基因组")
            return []

        logger.info(f"第二阶段：开始批量预测 {len(failed_genomes)} 个基因组的特征")

        results = []
        start_time = time.time()

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            future_to_genome = {}
            for genome_info in failed_genomes:
                future = executor.submit(self.predict_features_for_genome, genome_info)
                future_to_genome[future] = genome_info

            # 收集结果
            completed = 0
            for future in as_completed(future_to_genome):
                genome_info = future_to_genome[future]
                genome_id = genome_info['genome_id']

                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"处理基因组 {genome_id} 时出错: {e}")
                    results.append({
                        'genome_id': genome_id,
                        'status': 'error',
                        'error': str(e),
                        'CDS_count': genome_info['original_cds'],
                        'tRNA_count': genome_info['original_trna'],
                        'rRNA_count': genome_info['original_rrna']
                    })
                    self.stats['prediction_failed'] += 1

                completed += 1
                elapsed = time.time() - start_time
                progress = completed / len(failed_genomes) * 100
                eta = (elapsed / completed) * (len(failed_genomes) - completed) if completed > 0 else 0

                logger.info(f"预测进度: {completed}/{len(failed_genomes)} ({progress:.1f}%) - "
                          f"用时: {elapsed:.1f}s, ETA: {eta:.1f}s")

        return results

    def update_final_statistics(self, original_statistics: List[Dict[str, Any]],
                              prediction_results: List[Dict[str, Any]]):
        """
        更新最终统计文件

        Args:
            original_statistics: 原始统计数据
            prediction_results: 预测结果
        """
        try:
            # 创建结果映射
            result_map = {result['genome_id']: result for result in prediction_results}

            # 重新加载当前统计文件（可能已被重新提取更新）
            current_statistics = []
            stats_file = self.output_dir / 'feature_statistics.csv'
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    current_statistics = list(reader)
            else:
                current_statistics = original_statistics

            # 更新统计数据
            updated_statistics = []
            for row in current_statistics:
                genome_id = row['genome_id']
                if genome_id in result_map:
                    result = result_map[genome_id]
                    if result['status'] == 'success':
                        # 更新预测结果
                        row['CDS_count'] = result['CDS_count']
                        row['tRNA_count'] = result['tRNA_count']
                        row['rRNA_count'] = result['rRNA_count']
                        row['status'] = 'predicted'
                        row['error'] = ''
                    else:
                        # 更新失败结果
                        row['status'] = 'prediction_failed'
                        row['error'] = result.get('error', 'Prediction failed')[:100]

                updated_statistics.append(row)

            # 备份原文件
            backup_file = self.statistics_file.with_suffix('.csv.unified_backup')
            if stats_file.exists():
                shutil.copy2(stats_file, backup_file)
                logger.info(f"统计文件已备份到: {backup_file}")

            # 写入更新后的统计文件
            with open(self.statistics_file, 'w', newline='', encoding='utf-8') as f:
                if updated_statistics:
                    fieldnames = ['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(updated_statistics)

            logger.info(f"最终统计文件已更新: {self.statistics_file}")

        except Exception as e:
            logger.error(f"更新最终统计文件失败: {e}")
            raise

    def generate_unified_report(self, original_genomes: List[Dict[str, Any]],
                              prediction_results: List[Dict[str, Any]]):
        """
        生成统一处理报告

        Args:
            original_genomes: 原始需要处理的基因组列表
            prediction_results: 预测结果
        """
        try:
            report_file = self.output_dir / 'unified_processing_report.txt'

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("统一基因组特征处理报告\n")
                f.write("=" * 60 + "\n\n")

                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总基因组数: {self.stats['total_genomes']}\n")
                f.write(f"需要处理: {self.stats['need_processing']}\n\n")

                f.write("第一阶段 - 传统重新提取:\n")
                f.write("-" * 30 + "\n")
                f.write(f"重新提取成功: {self.stats['reextraction_success']}\n")
                f.write(f"重新提取失败: {self.stats['reextraction_failed']}\n\n")

                if self.enable_prediction:
                    f.write("第二阶段 - 预测工具处理:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"预测成功: {self.stats['prediction_success']}\n")
                    f.write(f"预测失败: {self.stats['prediction_failed']}\n\n")

                f.write("最终结果:\n")
                f.write("-" * 30 + "\n")
                total_success = self.stats['reextraction_success'] + self.stats['prediction_success']
                total_failed = self.stats['reextraction_failed'] + self.stats['prediction_failed'] - self.stats['prediction_success']
                f.write(f"总成功: {total_success}\n")
                f.write(f"仍然失败: {total_failed}\n")
                if self.stats['need_processing'] > 0:
                    success_rate = total_success / self.stats['need_processing'] * 100
                    f.write(f"总成功率: {success_rate:.1f}%\n")

                f.write(f"\nconda环境: {self.conda_env}\n")
                f.write(f"预测工具: {'启用' if self.enable_prediction else '禁用'}\n")

            logger.info(f"统一处理报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"生成统一处理报告失败: {e}")

    def cleanup_temp_files(self, temp_metadata: Path):
        """
        清理临时文件

        Args:
            temp_metadata: 临时元数据文件路径
        """
        try:
            if temp_metadata.exists():
                temp_metadata.unlink()
                logger.info(f"已删除临时文件: {temp_metadata}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {e}")

    def run(self):
        """
        运行统一特征处理流程
        """
        temp_metadata = None
        try:
            start_time = time.time()

            logger.info("=" * 60)
            logger.info("开始统一基因组特征处理流程")
            logger.info("=" * 60)

            # 加载统计数据
            logger.info("加载特征统计文件...")
            original_statistics = self.load_statistics()

            # 识别需要处理的基因组
            logger.info("识别需要处理的基因组...")
            genomes_to_process = self.identify_genomes_for_processing(original_statistics)

            if not genomes_to_process:
                logger.info("没有需要处理的基因组")
                return

            # 第一阶段：传统重新提取
            logger.info("=" * 60)
            logger.info("第一阶段：传统特征重新提取")
            logger.info("=" * 60)

            # 创建临时元数据文件
            temp_metadata = self.create_temp_metadata_file(genomes_to_process)

            # 运行传统特征提取
            reextraction_success = self.run_genome_feature_extractor(temp_metadata)

            if reextraction_success:
                # 分析重新提取结果
                still_failed_genomes = self.analyze_reextraction_results(genomes_to_process)
            else:
                logger.warning("传统特征提取失败，将所有基因组标记为需要预测")
                still_failed_genomes = genomes_to_process
                self.stats['reextraction_failed'] = len(genomes_to_process)

            # 第二阶段：预测工具处理
            prediction_results = []
            if self.enable_prediction and still_failed_genomes:
                logger.info("=" * 60)
                logger.info("第二阶段：使用预测工具处理仍然失败的基因组")
                logger.info("=" * 60)

                prediction_results = self.batch_predict_features(still_failed_genomes)

                # 更新最终统计文件
                logger.info("更新最终统计文件...")
                self.update_final_statistics(original_statistics, prediction_results)
            elif not self.enable_prediction:
                logger.info("预测工具已禁用，跳过第二阶段")
            else:
                logger.info("没有需要预测的基因组，跳过第二阶段")

            # 生成统一报告
            logger.info("生成统一处理报告...")
            self.generate_unified_report(genomes_to_process, prediction_results)

            # 输出最终摘要
            elapsed_time = time.time() - start_time
            total_success = self.stats['reextraction_success'] + self.stats['prediction_success']
            total_failed = self.stats['need_processing'] - total_success

            logger.info("=" * 60)
            logger.info("统一特征处理完成!")
            logger.info("=" * 60)
            logger.info(f"总用时: {elapsed_time:.1f} 秒")
            logger.info(f"总基因组数: {self.stats['total_genomes']}")
            logger.info(f"需要处理: {self.stats['need_processing']}")
            logger.info(f"重新提取成功: {self.stats['reextraction_success']}")
            logger.info(f"预测工具成功: {self.stats['prediction_success']}")
            logger.info(f"总成功: {total_success}")
            logger.info(f"仍然失败: {total_failed}")

            if self.stats['need_processing'] > 0:
                success_rate = total_success / self.stats['need_processing'] * 100
                logger.info(f"总成功率: {success_rate:.1f}%")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"运行统一特征处理流程失败: {e}", exc_info=True)
            raise
        finally:
            # 清理临时文件
            if temp_metadata:
                self.cleanup_temp_files(temp_metadata)


def main():
    parser = argparse.ArgumentParser(
        description='统一基因组特征处理器 - 融合重新提取和预测工具的完整解决方案',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--statistics_file', default='extracted_features_seq/feature_statistics.csv',
                       help='特征统计文件路径')
    parser.add_argument('--output_dir', default='extracted_features_seq',
                       help='输出目录')
    parser.add_argument('--root_dir', default=None,
                       help='基因组数据根目录')
    parser.add_argument('--threads', type=int, default=4,
                       help='线程数')
    parser.add_argument('--conda_env', default='genome_analysis',
                       help='conda环境名称')
    parser.add_argument('--disable_prediction', action='store_true',
                       help='禁用预测工具，仅进行传统重新提取')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))

    # 创建统一处理器并运行
    processor = UnifiedFeatureProcessor(
        statistics_file=args.statistics_file,
        output_dir=args.output_dir,
        root_dir=args.root_dir,
        threads=args.threads,
        conda_env=args.conda_env,
        enable_prediction=not args.disable_prediction
    )

    processor.run()


if __name__ == '__main__':
    main()
