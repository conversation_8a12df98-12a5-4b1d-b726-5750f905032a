# 增强版基因组特征预测器使用指南

## 概述

`enhanced_feature_predictor.py` 是一个使用多种预测工具来处理传统方法无法提取特征的基因组的高级工具。它专门针对那些CDS、tRNA或rRNA计数仍然为0的基因组，使用专业的预测软件来生成特征。

## 主要功能

1. **多工具预测**：
   - **Prodigal**: CDS预测（业界标准）
   - **tRNAscan-SE**: tRNA预测（高精度）
   - **Barrnap**: rRNA预测（快速准确）

2. **智能处理**：
   - 自动识别需要预测的特征类型
   - 根据kingdom选择合适的预测模型
   - 并行处理提高效率

3. **完整流程**：
   - 自动查找基因组序列文件
   - 处理压缩文件
   - 生成标准格式输出
   - 更新统计文件

## 安装和配置

### 1. 运行安装脚本

```bash
# 给脚本执行权限
chmod +x setup_prediction_tools.sh

# 运行安装脚本
./setup_prediction_tools.sh
```

### 2. 手动安装（如果脚本失败）

```bash
# 激活conda环境
conda activate genome_analysis

# 安装预测工具
conda install -c bioconda prodigal trnascan-se barrnap -y

# 安装Python依赖
pip install biopython requests pandas numpy
```

### 3. 验证安装

```bash
# 运行测试脚本
conda activate genome_analysis
python test_prediction_tools.py
```

## 使用方法

### 基本用法

```bash
# 在conda环境中运行
conda run -n genome_analysis python enhanced_feature_predictor.py
```

### 高级用法

```bash
conda run -n genome_analysis python enhanced_feature_predictor.py \
    --statistics_file extracted_features_seq/feature_statistics.csv \
    --output_dir extracted_features_seq \
    --threads 4 \
    --log_level INFO
```

### 参数说明

- `--statistics_file`: 特征统计文件路径
- `--output_dir`: 输出目录
- `--root_dir`: 基因组数据根目录
- `--threads`: 线程数（建议2-4个）
- `--conda_env`: conda环境名称
- `--disable_online_tools`: 禁用在线预测工具
- `--log_level`: 日志级别

## 预测工具详解

### 1. Prodigal (CDS预测)
- **用途**: 预测蛋白质编码序列
- **特点**: 
  - 无监督学习，自动训练模型
  - 适用于细菌和古菌基因组
  - 高精度，业界标准工具
- **输出**: 压缩的FASTA格式CDS序列

### 2. tRNAscan-SE (tRNA预测)
- **用途**: 预测转移RNA基因
- **特点**:
  - 结合多种算法（tRNAscan + EufindtRNA）
  - 支持细菌、古菌、真核生物模式
  - 高灵敏度和特异性
- **输出**: 压缩的FASTA格式tRNA序列

### 3. Barrnap (rRNA预测)
- **用途**: 预测核糖体RNA基因
- **特点**:
  - 基于nhmmer和Rfam数据库
  - 支持细菌、古菌、真核生物模型
  - 快速准确
- **输出**: 压缩的FASTA格式rRNA序列

## 处理流程

1. **加载失败基因组**: 从统计文件中识别特征计数为0的基因组
2. **查找序列文件**: 在指定目录结构中查找基因组序列文件
3. **预测特征**: 根据需要使用相应的预测工具
4. **生成输出**: 将预测结果保存为压缩的FASTA文件
5. **更新统计**: 更新特征统计文件
6. **生成报告**: 创建详细的预测报告

## 输出文件

```
extracted_features_seq/
├── feature_statistics.csv                    # 更新后的统计文件
├── feature_statistics.csv.prediction_backup  # 备份文件
├── prediction_report.txt                     # 预测报告
├── CDS/
│   ├── [genome_id]_CDS.fasta.gz             # 预测的CDS序列
├── tRNA/
│   ├── [genome_id]_tRNA.fasta.gz            # 预测的tRNA序列
├── rRNA/
│   ├── [genome_id]_rRNA.fasta.gz            # 预测的rRNA序列
└── enhanced_feature_predictor.log            # 详细日志
```

## 当前状态分析

基于测试结果，当前有一些基因组的特征计数仍然为0：

```bash
# 检查当前失败状态
conda run -n genome_analysis python -c "
import csv
failed = []
with open('extracted_features_seq/feature_statistics.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        cds = int(row.get('CDS_count', 0))
        trna = int(row.get('tRNA_count', 0))
        rrna = int(row.get('rRNA_count', 0))
        if cds == 0 or trna == 0 or rrna == 0:
            failed.append(row)

print(f'仍需预测的基因组数量: {len(failed)}')
for i, row in enumerate(failed[:5]):
    print(f'  {i+1}. {row[\"genome_id\"]}: CDS={row.get(\"CDS_count\", 0)}, tRNA={row.get(\"tRNA_count\", 0)}, rRNA={row.get(\"rRNA_count\", 0)}')
"
```

## 性能预估

基于预测工具的特性：
- **Prodigal**: ~30秒/基因组（中等大小）
- **tRNAscan-SE**: ~60秒/基因组
- **Barrnap**: ~15秒/基因组

对于需要预测的基因组：
- 预计总用时：2-4小时（取决于基因组数量和大小）
- 建议使用2-4个线程并行处理
- 成功率预期：80-95%

## 预期改进效果

### CDS预测
- **改善对象**: CDS计数为0的基因组
- **预期成功率**: 95%+（Prodigal非常可靠）
- **典型结果**: 每个基因组1000-5000个CDS

### tRNA预测
- **改善对象**: tRNA计数为0的基因组
- **预期成功率**: 85-90%
- **典型结果**: 每个基因组20-100个tRNA

### rRNA预测
- **改善对象**: rRNA计数为0的基因组
- **预期成功率**: 60-80%（某些基因组确实缺少rRNA）
- **典型结果**: 每个基因组1-10个rRNA

## 故障排除

### 1. 工具未安装
```bash
# 检查工具可用性
which prodigal tRNAscan-SE barrnap

# 重新安装
conda install -c bioconda prodigal trnascan-se barrnap -y
```

### 2. 序列文件未找到
```bash
# 检查目录结构
ls -la Bacteria/GCF_*/
ls -la Archaea/GCA_*/

# 确保序列文件存在
find . -name "*.fna*" | head -10
```

### 3. 内存不足
```bash
# 减少线程数
--threads 2

# 监控内存使用
htop
```

### 4. 预测失败
```bash
# 检查详细日志
tail -100 enhanced_feature_predictor.log

# 手动测试单个工具
prodigal -i test.fna -d test_cds.fasta
```

## 最佳实践

1. **分批处理**: 对于大量基因组，建议分批处理
2. **资源监控**: 监控CPU和内存使用情况
3. **日志检查**: 定期检查日志文件
4. **结果验证**: 验证预测结果的合理性
5. **备份数据**: 处理前备份重要文件

## 后续步骤

1. **运行预测器**: 处理所有失败的基因组
2. **验证结果**: 检查预测质量和数量
3. **更新分析**: 使用新的特征数据重新进行分析
4. **文档记录**: 记录预测过程和结果

## 联系支持

如遇到问题：
1. 检查日志文件获取详细错误信息
2. 验证工具安装和环境配置
3. 确认输入文件格式和路径
4. 联系技术支持获取帮助
