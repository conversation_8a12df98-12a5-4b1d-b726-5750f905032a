# 蛋白质序列下载器使用指南

## 概述

`protein_sequence_downloader.py` 是一个专门用于下载失败蛋白质序列的工具，可以从多个数据库（NCBI、UniProt、EBI）下载微生物基因组的蛋白质序列。

## 主要功能

1. **多数据库支持**：优先从NCBI下载，失败时自动尝试UniProt和EBI
2. **智能重试机制**：每个数据库最多重试3次，避免临时网络问题
3. **并行下载**：支持多线程并行下载，提高效率
4. **断点续传**：自动跳过已存在的文件，支持中断后继续
5. **目录结构管理**：按kingdom分类存储到对应目录
6. **详细日志**：完整的下载日志和进度跟踪
7. **结果统计**：生成详细的下载报告和统计信息

## 安装依赖

```bash
pip install requests biopython
```

## 使用方法

### 基本用法

```bash
python protein_sequence_downloader.py extracted_features_seq/protein_seq/failed_accessions_kingdom.csv
```

### 高级用法

```bash
python protein_sequence_downloader.py failed_accessions_kingdom.csv \
    --output_dir extracted_features_seq/protein_seq \
    --threads 4 \
    --max_retries 3 \
    --delay 1.0 \
    --log_level INFO
```

### 参数说明

- `failed_file`: 失败accession的CSV文件路径（必需）
- `--output_dir`: 输出目录（默认: extracted_features_seq/protein_seq）
- `--threads`: 线程数（默认: 4，建议不超过8避免被服务器封禁）
- `--max_retries`: 最大重试次数（默认: 3）
- `--delay`: 请求间延迟秒数（默认: 1.0）
- `--log_level`: 日志级别（DEBUG, INFO, WARNING, ERROR）

## 输入文件格式

输入的CSV文件必须包含以下列：
- `accession`: 基因组accession号（如GCF_000008365.1）
- `kingdom`: 生物界分类（如Bacteria, Archaea, Fungi等）

示例：
```csv
accession,kingdom
GCF_904846055.1,Bacteria
GCA_049306585.1,Fungi
GCA_027474885.1,Bacteria
```

## 输出结构

下载的蛋白质序列按kingdom分类存储：

```
extracted_features_seq/protein_seq/
├── Bacteria/
│   ├── GCA_027474885.1_protein.faa.gz
│   └── ...
├── Fungi/
│   ├── GCA_049306585.1_protein.faa.gz
│   └── ...
├── Archaea/
├── Algae/
├── successful_downloads.csv      # 成功下载的列表
├── still_failed_accessions.csv  # 仍然失败的列表
├── download_summary.txt          # 下载摘要报告
└── protein_downloader.log        # 详细日志
```

## 下载策略

脚本按以下优先级尝试下载：

1. **NCBI FTP服务器**
   - 尝试多种文件名格式
   - 直接从官方FTP下载

2. **NCBI E-utilities API**
   - 通过API搜索和下载
   - 适合处理复杂查询

3. **UniProt数据库**
   - 通过REST API搜索
   - 提供高质量注释

4. **EBI数据库**
   - 欧洲生物信息学研究所
   - 备用数据源

## 性能优化建议

1. **线程数设置**：建议使用4-8个线程，过多可能被服务器限制
2. **请求延迟**：设置适当延迟（1-2秒）避免被封禁
3. **批量处理**：对于大量文件，可以分批处理
4. **网络环境**：确保网络连接稳定

## 错误处理

脚本具有完善的错误处理机制：

- **网络错误**：自动重试，记录详细错误信息
- **文件格式错误**：跳过无效条目，继续处理其他文件
- **权限错误**：检查目录权限，提供清晰错误信息
- **数据库错误**：尝试其他数据库，记录失败原因

## 监控和日志

- **实时进度**：显示当前进度和成功率
- **详细日志**：记录每个下载尝试的详细信息
- **统计报告**：生成完整的下载统计和摘要

## 示例输出

```
2025-08-20 15:34:58,033 - INFO - 初始化蛋白质序列下载器: 线程数=2, 重试次数=3
2025-08-20 15:34:58,034 - INFO - 成功加载 3 个失败的accession
2025-08-20 15:34:58,034 - INFO - 开始批量下载 3 个蛋白质序列
...
2025-08-20 15:35:53,438 - INFO - 成功下载并保存: GCA_049306585.1 使用 EBI
2025-08-20 15:36:05,375 - INFO - 成功下载并保存: GCA_027474885.1 使用 EBI
...
2025-08-20 15:36:05,879 - INFO - 下载完成!
2025-08-20 15:36:05,879 - INFO - 总用时: 67.8 秒
2025-08-20 15:36:05,879 - INFO - 总数: 3
2025-08-20 15:36:05,879 - INFO - 成功: 2
2025-08-20 15:36:05,879 - INFO - 失败: 1
2025-08-20 15:36:05,879 - INFO - 成功率: 66.7%
```

## 故障排除

1. **网络连接问题**：检查网络连接，尝试降低线程数
2. **权限问题**：确保对输出目录有写权限
3. **磁盘空间**：确保有足够磁盘空间存储下载文件
4. **服务器限制**：如果被限制，增加延迟时间或减少线程数

## 注意事项

1. 请遵守各数据库的使用条款和访问限制
2. 避免过于频繁的请求，以免被服务器封禁
3. 大批量下载时建议在非高峰时段进行
4. 定期检查和清理日志文件

## 联系支持

如遇到问题，请检查日志文件获取详细错误信息，或联系技术支持。
