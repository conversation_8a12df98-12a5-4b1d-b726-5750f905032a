[{"seq_id": "CAXPUL010000014.1", "start": 3091, "end": 3200, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000027.1", "start": 23674, "end": 23768, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000027.1", "start": 1767, "end": 1693, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000027.1", "start": 1644, "end": 1569, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000028.1", "start": 703, "end": 618, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000034.1", "start": 31156, "end": 31233, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000034.1", "start": 19193, "end": 19119, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000036.1", "start": 36855, "end": 36773, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000036.1", "start": 36632, "end": 36560, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000036.1", "start": 36538, "end": 36464, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000036.1", "start": 34959, "end": 34885, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000039.1", "start": 30678, "end": 30603, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000043.1", "start": 22558, "end": 22484, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000045.1", "start": 23849, "end": 23934, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000045.1", "start": 11872, "end": 11786, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000055.1", "start": 452, "end": 378, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000057.1", "start": 5001, "end": 4927, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000064.1", "start": 4688, "end": 4762, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000074.1", "start": 107382, "end": 107308, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000074.1", "start": 74156, "end": 74072, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000074.1", "start": 60525, "end": 60450, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000077.1", "start": 40815, "end": 40892, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000077.1", "start": 50644, "end": 50569, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000077.1", "start": 50513, "end": 50439, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000088.1", "start": 87612, "end": 87538, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000098.1", "start": 18976, "end": 19067, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000103.1", "start": 26691, "end": 26766, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000103.1", "start": 30303, "end": 30380, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000110.1", "start": 4491, "end": 4567, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000110.1", "start": 4573, "end": 4649, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000111.1", "start": 363, "end": 466, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000112.1", "start": 1691, "end": 1616, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAXPUL010000136.1", "start": 59539, "end": 59465, "strand": "-", "feature_type": "tRNA"}]