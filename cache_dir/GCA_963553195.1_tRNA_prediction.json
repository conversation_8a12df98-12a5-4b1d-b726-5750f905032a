[{"seq_id": "CAUVNA010000001.1", "start": 52986, "end": 52911, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000001.1", "start": 639, "end": 565, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000004.1", "start": 2070, "end": 2147, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000004.1", "start": 2240, "end": 2314, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000004.1", "start": 17101, "end": 17177, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000008.1", "start": 4993, "end": 5085, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000008.1", "start": 5188, "end": 5281, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000009.1", "start": 9499, "end": 9576, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000010.1", "start": 7257, "end": 7333, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000010.1", "start": 15321, "end": 15397, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000012.1", "start": 8443, "end": 8369, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000015.1", "start": 15356, "end": 15283, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000024.1", "start": 11187, "end": 11272, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000034.1", "start": 6409, "end": 6485, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000035.1", "start": 9280, "end": 9207, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000040.1", "start": 2057, "end": 1972, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000042.1", "start": 6086, "end": 6161, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000049.1", "start": 2671, "end": 2748, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000052.1", "start": 5587, "end": 5678, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000052.1", "start": 8766, "end": 8854, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000054.1", "start": 6786, "end": 6712, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000055.1", "start": 8703, "end": 8777, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000055.1", "start": 8780, "end": 8857, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000068.1", "start": 490, "end": 408, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000069.1", "start": 1548, "end": 1622, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000069.1", "start": 6483, "end": 6400, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000069.1", "start": 6393, "end": 6319, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000069.1", "start": 6225, "end": 6150, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000069.1", "start": 4567, "end": 4492, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000070.1", "start": 7080, "end": 7178, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000076.1", "start": 5121, "end": 5206, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000080.1", "start": 2505, "end": 2432, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000082.1", "start": 3168, "end": 3093, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000082.1", "start": 3016, "end": 2942, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000096.1", "start": 1757, "end": 1832, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000096.1", "start": 1869, "end": 1944, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000098.1", "start": 2497, "end": 2423, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000144.1", "start": 1767, "end": 1694, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000160.1", "start": 0, "end": 65, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000160.1", "start": 157, "end": 232, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUVNA010000161.1", "start": 645, "end": 731, "strand": "-", "feature_type": "tRNA"}]