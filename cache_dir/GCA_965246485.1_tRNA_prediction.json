[{"seq_id": "CBDFEI010000002.1", "start": 3, "end": 80, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000007.1", "start": 1029, "end": 1105, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000007.1", "start": 1122, "end": 1198, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000008.1", "start": 1059, "end": 1150, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000008.1", "start": 1271, "end": 1201, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000012.1", "start": 4749, "end": 4661, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000095.1", "start": 3043, "end": 2958, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000119.1", "start": 470, "end": 388, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000119.1", "start": 352, "end": 280, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000119.1", "start": 267, "end": 194, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000122.1", "start": 2229, "end": 2305, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000127.1", "start": 891, "end": 967, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000131.1", "start": 2858, "end": 2944, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000157.1", "start": 3081, "end": 3007, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000157.1", "start": 2963, "end": 2888, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000241.1", "start": 1858, "end": 1935, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000241.1", "start": 1947, "end": 2022, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000330.1", "start": 1998, "end": 1923, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000386.1", "start": 1684, "end": 1610, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000396.1", "start": 79, "end": 4, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CBDFEI010000428.1", "start": 552, "end": 410, "strand": "-", "feature_type": "tRNA"}]