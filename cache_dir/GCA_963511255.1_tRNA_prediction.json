[{"seq_id": "CAUPIO010000008.1", "start": 7382, "end": 7467, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000008.1", "start": 4102, "end": 4020, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000026.1", "start": 309, "end": 383, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000026.1", "start": 432, "end": 506, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000027.1", "start": 5701, "end": 5773, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000027.1", "start": 6157, "end": 6229, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000033.1", "start": 6254, "end": 6181, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000042.1", "start": 2614, "end": 2539, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000053.1", "start": 1992, "end": 2065, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000053.1", "start": 2077, "end": 2164, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000081.1", "start": 6849, "end": 6778, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000084.1", "start": 2333, "end": 2262, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000092.1", "start": 2431, "end": 2351, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000108.1", "start": 5455, "end": 5375, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000108.1", "start": 5319, "end": 5248, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000108.1", "start": 5205, "end": 5135, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000108.1", "start": 3636, "end": 3565, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000122.1", "start": 274, "end": 348, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000132.1", "start": 551, "end": 468, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000157.1", "start": 3067, "end": 2981, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000170.1", "start": 2185, "end": 2115, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000177.1", "start": 309, "end": 383, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000193.1", "start": 3548, "end": 3475, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000193.1", "start": 3447, "end": 3371, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000197.1", "start": 681, "end": 610, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000213.1", "start": 585, "end": 653, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000213.1", "start": 3182, "end": 3265, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000213.1", "start": 881, "end": 796, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000222.1", "start": 500, "end": 573, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000239.1", "start": 76, "end": 160, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPIO010000250.1", "start": 179, "end": 256, "strand": "-", "feature_type": "tRNA"}]