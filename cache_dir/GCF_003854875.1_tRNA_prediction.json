[{"seq_id": "CP033953.1", "start": 10994, "end": 11084, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 19137, "end": 19233, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 238009, "end": 238100, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 246062, "end": 246138, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 397558, "end": 397635, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 676904, "end": 676981, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 684345, "end": 684422, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 985053, "end": 985129, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1365406, "end": 1365483, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1365522, "end": 1365599, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1365614, "end": 1365690, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1651006, "end": 1651082, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1651144, "end": 1651229, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1651298, "end": 1651374, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1651394, "end": 1651469, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1652842, "end": 1652918, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2132384, "end": 2132460, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2132521, "end": 2132595, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2132600, "end": 2132676, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2132745, "end": 2132832, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2678798, "end": 2678874, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2783823, "end": 2783900, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2987601, "end": 2987526, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2987308, "end": 2987234, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2854900, "end": 2854817, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2704479, "end": 2704404, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2545713, "end": 2545636, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2538892, "end": 2538809, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 2265891, "end": 2265808, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 1119728, "end": 1119656, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 856038, "end": 855964, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 538144, "end": 538052, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 537931, "end": 537856, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 510663, "end": 510588, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 502897, "end": 502822, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 447478, "end": 447395, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 341823, "end": 341735, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 55230, "end": 55147, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 48023, "end": 47949, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 47783, "end": 47708, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 32556, "end": 32482, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CP033953.1", "start": 32392, "end": 32318, "strand": "-", "feature_type": "tRNA"}]