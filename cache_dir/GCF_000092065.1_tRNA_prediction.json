[{"seq_id": "NC_009355.1", "start": 484211, "end": 484394, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009355.1", "start": 578886, "end": 578959, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 411744, "end": 411816, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 450919, "end": 450992, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 806889, "end": 807033, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 552788, "end": 552673, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 450829, "end": 450751, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009356.1", "start": 425904, "end": 425833, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009357.1", "start": 89617, "end": 89716, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009357.1", "start": 89766, "end": 89866, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009357.1", "start": 89916, "end": 90016, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009358.1", "start": 789310, "end": 789397, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009358.1", "start": 418709, "end": 418529, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009360.1", "start": 630948, "end": 630774, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009360.1", "start": 357127, "end": 357057, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009360.1", "start": 136827, "end": 136757, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009361.1", "start": 295381, "end": 295511, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009361.1", "start": 351669, "end": 351740, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009361.1", "start": 245601, "end": 245439, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009362.1", "start": 519718, "end": 519523, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009363.1", "start": 183353, "end": 183283, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009364.1", "start": 327595, "end": 327706, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009364.1", "start": 554247, "end": 554102, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009365.1", "start": 338691, "end": 338765, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009365.1", "start": 300429, "end": 300244, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009365.1", "start": 297992, "end": 297807, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009365.1", "start": 291982, "end": 291868, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009366.1", "start": 478404, "end": 478305, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009367.1", "start": 393483, "end": 393597, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009368.1", "start": 585503, "end": 585575, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009368.1", "start": 571750, "end": 571620, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009368.1", "start": 561843, "end": 561739, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009368.1", "start": 312170, "end": 312090, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009368.1", "start": 169344, "end": 169264, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009369.1", "start": 165001, "end": 164829, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009370.1", "start": 101197, "end": 101354, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009370.1", "start": 70414, "end": 70139, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009373.1", "start": 100877, "end": 101005, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009373.1", "start": 9019, "end": 8948, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009374.1", "start": 65789, "end": 65950, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009374.1", "start": 48012, "end": 47885, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "NC_009375.1", "start": 134984, "end": 134872, "strand": "-", "feature_type": "tRNA"}]