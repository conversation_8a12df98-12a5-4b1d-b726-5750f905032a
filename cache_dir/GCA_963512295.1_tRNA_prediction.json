[{"seq_id": "CAUPMV010000018.1", "start": 856, "end": 950, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000047.1", "start": 870, "end": 795, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000067.1", "start": 3944, "end": 4031, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000087.1", "start": 2595, "end": 2507, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000114.1", "start": 115, "end": 191, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000142.1", "start": 131, "end": 208, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000182.1", "start": 2831, "end": 2779, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000199.1", "start": 676, "end": 602, "strand": "-", "feature_type": "tRNA"}, {"seq_id": "CAUPMV010000202.1", "start": 1044, "end": 972, "strand": "-", "feature_type": "tRNA"}]