#!/usr/bin/env python3
"""
蛋白质序列下载器
用于下载failed_accessions_kingdom.csv中失败的蛋白质序列

主要功能：
1. 从NCBI优先下载蛋白质序列
2. 如果NCBI失败，尝试其他数据库（UniProt, EBI等）
3. 按kingdom分类存储到对应目录
4. 支持断点续传和错误重试
5. 详细的日志记录和进度跟踪

作者：AI Assistant
日期：2025-08-20
版本：1.0
"""

import os
import sys
import csv
import gzip
import json
import logging
import argparse
import time
import urllib.request
import urllib.error
import urllib.parse
import requests
import xml.etree.ElementTree as ET
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
import tempfile
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('protein_downloader.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 生物界分类目录映射
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi', 
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

class ProteinSequenceDownloader:
    def __init__(self, output_dir='extracted_features_seq/protein_seq', threads=4, 
                 max_retries=3, delay_between_requests=1.0):
        """
        初始化蛋白质序列下载器
        
        Args:
            output_dir: 输出目录
            threads: 线程数
            max_retries: 最大重试次数
            delay_between_requests: 请求间延迟（秒）
        """
        self.output_dir = Path(output_dir)
        self.threads = max(1, min(threads, 8))  # 限制线程数避免被服务器封禁
        self.max_retries = max_retries
        self.delay = delay_between_requests
        
        # 创建输出目录结构
        self.output_dir.mkdir(exist_ok=True, parents=True)
        for kingdom in KINGDOM_DIR_MAPPING.values():
            (self.output_dir / kingdom).mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 会话对象用于连接复用
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (compatible; ProteinDownloader/1.0; +https://example.com/contact)'
        })
        
        logger.info(f"初始化蛋白质序列下载器: 线程数={self.threads}, 重试次数={self.max_retries}")
    
    def load_failed_accessions(self, failed_file: str) -> List[Dict[str, str]]:
        """
        加载失败的accession列表
        
        Args:
            failed_file: 失败accession的CSV文件路径
            
        Returns:
            list: 包含accession和kingdom的字典列表
        """
        accessions = []
        try:
            with open(failed_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    accession = row.get('accession', '').strip()
                    kingdom = row.get('kingdom', '').strip()
                    
                    if accession and kingdom:
                        # 规范化kingdom名称
                        kingdom = kingdom.capitalize()
                        if kingdom not in KINGDOM_DIR_MAPPING:
                            logger.warning(f"未知的kingdom '{kingdom}'，使用默认处理")
                            kingdom = 'Bacteria'  # 默认为细菌
                        
                        accessions.append({
                            'accession': accession,
                            'kingdom': kingdom
                        })
                    else:
                        logger.warning(f"跳过无效行: accession='{accession}', kingdom='{kingdom}'")
            
            logger.info(f"成功加载 {len(accessions)} 个失败的accession")
            return accessions
        except FileNotFoundError:
            logger.error(f"失败accession文件未找到: {failed_file}")
            raise
        except Exception as e:
            logger.error(f"加载失败accession文件出错: {e}")
            raise
    
    def check_existing_file(self, accession: str, kingdom: str) -> bool:
        """
        检查蛋白质序列文件是否已存在
        
        Args:
            accession: 基因组accession
            kingdom: 生物界分类
            
        Returns:
            bool: 文件是否存在
        """
        kingdom_dir = KINGDOM_DIR_MAPPING.get(kingdom, kingdom)
        protein_file = self.output_dir / kingdom_dir / f"{accession}_protein.faa.gz"
        return protein_file.exists() and protein_file.stat().st_size > 0
    
    def download_from_ncbi_ftp(self, accession: str) -> Optional[bytes]:
        """
        从NCBI FTP服务器下载蛋白质序列
        
        Args:
            accession: 基因组accession
            
        Returns:
            bytes: 蛋白质序列数据，如果失败则返回None
        """
        try:
            # 构造NCBI FTP URL
            if not accession.startswith(('GCF_', 'GCA_')):
                logger.warning(f"Accession格式不正确: {accession}")
                return None
            
            parts = accession.split('_')
            if len(parts) < 2:
                logger.warning(f"Accession格式不正确: {accession}")
                return None
            
            accession_prefix = parts[0]  # GCF or GCA
            numeric_part = parts[1].split('.')[0]  # 去掉版本号
            
            # 构造路径
            numeric_part = numeric_part.zfill(9)  # 补齐到9位
            path_parts = [numeric_part[i:i+3] for i in range(0, 9, 3)]
            
            ftp_url_base = f"ftp://ftp.ncbi.nlm.nih.gov/genomes/all/{accession_prefix}/{path_parts[0]}/{path_parts[1]}/{path_parts[2]}/{accession}"
            
            # 尝试不同的蛋白质文件名
            possible_filenames = [
                f"{accession}_protein.faa.gz",
                f"{accession}_translated_cds.faa.gz",
                f"{accession}_cds_from_genomic.fna.gz"
            ]
            
            for filename in possible_filenames:
                url = f"{ftp_url_base}/{filename}"
                try:
                    logger.debug(f"尝试从NCBI FTP下载: {url}")
                    with urllib.request.urlopen(url, timeout=30) as response:
                        data = response.read()
                        if len(data) > 0:
                            logger.info(f"成功从NCBI FTP下载: {filename}")
                            return data
                except urllib.error.URLError as e:
                    logger.debug(f"NCBI FTP下载失败 {url}: {e}")
                    continue
                except Exception as e:
                    logger.debug(f"NCBI FTP下载出错 {url}: {e}")
                    continue
            
            return None
        except Exception as e:
            logger.error(f"NCBI FTP下载过程出错: {e}")
            return None
    
    def download_from_ncbi_eutils(self, accession: str) -> Optional[bytes]:
        """
        使用NCBI E-utilities API下载蛋白质序列
        
        Args:
            accession: 基因组accession
            
        Returns:
            bytes: 蛋白质序列数据，如果失败则返回None
        """
        try:
            # 首先搜索获取蛋白质ID
            search_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
            search_params = {
                'db': 'protein',
                'term': f'{accession}[Assembly Accession]',
                'retmax': 10000,
                'retmode': 'xml'
            }
            
            logger.debug(f"搜索NCBI蛋白质: {accession}")
            response = self.session.get(search_url, params=search_params, timeout=30)
            response.raise_for_status()
            
            # 解析搜索结果
            root = ET.fromstring(response.content)
            id_list = root.find('.//IdList')
            if id_list is None or len(id_list) == 0:
                logger.debug(f"NCBI中未找到蛋白质序列: {accession}")
                return None
            
            # 获取蛋白质ID列表
            protein_ids = [id_elem.text for id_elem in id_list.findall('Id')]
            if not protein_ids:
                return None
            
            # 批量下载蛋白质序列
            fetch_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
            fetch_params = {
                'db': 'protein',
                'id': ','.join(protein_ids[:1000]),  # 限制数量避免超时
                'rettype': 'fasta',
                'retmode': 'text'
            }
            
            logger.debug(f"下载NCBI蛋白质序列: {len(protein_ids)} 条")
            response = self.session.get(fetch_url, params=fetch_params, timeout=60)
            response.raise_for_status()
            
            if response.text.strip():
                # 压缩数据
                import gzip
                return gzip.compress(response.text.encode('utf-8'))
            
            return None
        except Exception as e:
            logger.debug(f"NCBI E-utilities下载失败: {e}")
            return None
    
    def download_from_uniprot(self, accession: str) -> Optional[bytes]:
        """
        从UniProt下载蛋白质序列
        
        Args:
            accession: 基因组accession
            
        Returns:
            bytes: 蛋白质序列数据，如果失败则返回None
        """
        try:
            # UniProt REST API搜索
            search_url = "https://rest.uniprot.org/uniprotkb/search"
            search_params = {
                'query': f'genome_assembly:{accession}',
                'format': 'fasta',
                'size': 500  # 限制结果数量
            }
            
            logger.debug(f"搜索UniProt蛋白质: {accession}")
            response = self.session.get(search_url, params=search_params, timeout=30)
            response.raise_for_status()
            
            if response.text.strip():
                # 压缩数据
                import gzip
                return gzip.compress(response.text.encode('utf-8'))
            
            return None
        except Exception as e:
            logger.debug(f"UniProt下载失败: {e}")
            return None

    def download_from_ebi(self, accession: str) -> Optional[bytes]:
        """
        从EBI下载蛋白质序列

        Args:
            accession: 基因组accession

        Returns:
            bytes: 蛋白质序列数据，如果失败则返回None
        """
        try:
            # EBI REST API
            search_url = f"https://www.ebi.ac.uk/ena/browser/api/fasta/{accession}"

            logger.debug(f"搜索EBI蛋白质: {accession}")
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200 and response.text.strip():
                # 压缩数据
                import gzip
                return gzip.compress(response.text.encode('utf-8'))

            return None
        except Exception as e:
            logger.debug(f"EBI下载失败: {e}")
            return None

    def download_protein_sequence(self, accession: str, kingdom: str) -> bool:
        """
        下载单个蛋白质序列，按优先级尝试不同数据库

        Args:
            accession: 基因组accession
            kingdom: 生物界分类

        Returns:
            bool: 是否下载成功
        """
        # 检查文件是否已存在
        if self.check_existing_file(accession, kingdom):
            logger.info(f"蛋白质序列文件已存在，跳过: {accession}")
            self.stats['skipped'] += 1
            return True

        logger.info(f"开始下载蛋白质序列: {accession} ({kingdom})")

        # 按优先级尝试不同数据库
        download_methods = [
            ("NCBI FTP", self.download_from_ncbi_ftp),
            ("NCBI E-utilities", self.download_from_ncbi_eutils),
            ("UniProt", self.download_from_uniprot),
            ("EBI", self.download_from_ebi)
        ]

        for method_name, download_method in download_methods:
            for attempt in range(self.max_retries):
                try:
                    logger.debug(f"尝试 {method_name} (第 {attempt + 1} 次): {accession}")
                    data = download_method(accession)

                    if data and len(data) > 0:
                        # 保存文件
                        kingdom_dir = KINGDOM_DIR_MAPPING.get(kingdom, kingdom)
                        output_file = self.output_dir / kingdom_dir / f"{accession}_protein.faa.gz"

                        with open(output_file, 'wb') as f:
                            f.write(data)

                        logger.info(f"成功下载并保存: {accession} 使用 {method_name}")
                        self.stats['success'] += 1

                        # 添加延迟避免被封禁
                        time.sleep(self.delay)
                        return True

                except Exception as e:
                    logger.warning(f"{method_name} 下载失败 (第 {attempt + 1} 次): {accession} - {e}")

                # 重试前等待
                if attempt < self.max_retries - 1:
                    time.sleep(self.delay * (attempt + 1))

            # 方法间等待
            time.sleep(self.delay)

        logger.error(f"所有方法都失败，无法下载: {accession}")
        self.stats['failed'] += 1
        return False

    def download_batch(self, accessions: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        批量下载蛋白质序列

        Args:
            accessions: accession和kingdom的字典列表

        Returns:
            dict: 下载结果统计
        """
        self.stats['total'] = len(accessions)
        logger.info(f"开始批量下载 {len(accessions)} 个蛋白质序列")

        # 记录失败的accession
        failed_accessions = []
        successful_accessions = []

        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            future_to_accession = {}
            for acc_info in accessions:
                future = executor.submit(
                    self.download_protein_sequence,
                    acc_info['accession'],
                    acc_info['kingdom']
                )
                future_to_accession[future] = acc_info

            # 收集结果
            completed = 0
            for future in as_completed(future_to_accession):
                acc_info = future_to_accession[future]
                accession = acc_info['accession']
                kingdom = acc_info['kingdom']

                try:
                    success = future.result()
                    if success:
                        successful_accessions.append(acc_info)
                    else:
                        failed_accessions.append(acc_info)
                except Exception as e:
                    logger.error(f"处理 {accession} 时出错: {e}")
                    failed_accessions.append(acc_info)

                completed += 1
                # 显示进度
                progress = completed / len(accessions) * 100
                logger.info(f"进度: {completed}/{len(accessions)} ({progress:.1f}%) - "
                          f"成功: {self.stats['success']}, 失败: {self.stats['failed']}, 跳过: {self.stats['skipped']}")

        # 保存结果
        self.save_results(successful_accessions, failed_accessions)

        return {
            'total': self.stats['total'],
            'success': self.stats['success'],
            'failed': self.stats['failed'],
            'skipped': self.stats['skipped'],
            'failed_accessions': failed_accessions
        }

    def save_results(self, successful: List[Dict[str, str]], failed: List[Dict[str, str]]):
        """
        保存下载结果

        Args:
            successful: 成功下载的accession列表
            failed: 失败的accession列表
        """
        try:
            # 保存成功列表
            if successful:
                success_file = self.output_dir / 'successful_downloads.csv'
                with open(success_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['accession', 'kingdom'])
                    writer.writeheader()
                    writer.writerows(successful)
                logger.info(f"成功下载列表保存到: {success_file}")

            # 更新失败列表
            if failed:
                failed_file = self.output_dir / 'still_failed_accessions.csv'
                with open(failed_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['accession', 'kingdom'])
                    writer.writeheader()
                    writer.writerows(failed)
                logger.info(f"仍然失败的accession保存到: {failed_file}")

            # 保存下载摘要
            summary_file = self.output_dir / 'download_summary.txt'
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("蛋白质序列下载摘要\n")
                f.write("=" * 40 + "\n")
                f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总数: {self.stats['total']}\n")
                f.write(f"成功: {self.stats['success']}\n")
                f.write(f"失败: {self.stats['failed']}\n")
                f.write(f"跳过: {self.stats['skipped']}\n")
                f.write(f"成功率: {self.stats['success']/self.stats['total']*100:.1f}%\n")

            logger.info(f"下载摘要保存到: {summary_file}")

        except Exception as e:
            logger.error(f"保存结果时出错: {e}")

    def run(self, failed_file: str):
        """
        运行蛋白质序列下载流程

        Args:
            failed_file: 失败accession的CSV文件路径
        """
        try:
            start_time = time.time()

            # 加载失败的accession
            accessions = self.load_failed_accessions(failed_file)

            if not accessions:
                logger.warning("没有需要下载的accession")
                return

            # 批量下载
            results = self.download_batch(accessions)

            # 输出摘要
            elapsed_time = time.time() - start_time
            logger.info("=" * 60)
            logger.info("下载完成!")
            logger.info(f"总用时: {elapsed_time:.1f} 秒")
            logger.info(f"总数: {results['total']}")
            logger.info(f"成功: {results['success']}")
            logger.info(f"失败: {results['failed']}")
            logger.info(f"跳过: {results['skipped']}")
            logger.info(f"成功率: {results['success']/results['total']*100:.1f}%")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"运行蛋白质序列下载流程失败: {e}", exc_info=True)
            raise


def main():
    parser = argparse.ArgumentParser(
        description='蛋白质序列下载器 - 下载失败的蛋白质序列',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('failed_file', help='失败accession的CSV文件路径')
    parser.add_argument('--output_dir', default='extracted_features_seq/protein_seq',
                       help='输出目录 (默认: extracted_features_seq/protein_seq)')
    parser.add_argument('--threads', type=int, default=4, help='线程数 (默认: 4)')
    parser.add_argument('--max_retries', type=int, default=3, help='最大重试次数 (默认: 3)')
    parser.add_argument('--delay', type=float, default=1.0, help='请求间延迟秒数 (默认: 1.0)')
    parser.add_argument('--log_level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))

    # 创建下载器并运行
    downloader = ProteinSequenceDownloader(
        output_dir=args.output_dir,
        threads=args.threads,
        max_retries=args.max_retries,
        delay_between_requests=args.delay
    )

    downloader.run(args.failed_file)


if __name__ == '__main__':
    main()
