#!/usr/bin/env python3
"""
简化版基因组特征重新提取器
用于重新提取feature_statistics.csv中CDS、tRNA或rRNA计数为0的基因组特征

主要功能：
1. 分析feature_statistics.csv找出需要重新提取的基因组
2. 调用genome_feature_extractor.py重新处理这些基因组
3. 更新统计文件和输出目录

作者：AI Assistant
日期：2025-08-20
版本：1.0 (简化版)
"""

import os
import sys
import csv
import subprocess
import logging
import argparse
import time
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_feature_re_extractor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 生物界分类目录映射
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi', 
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

class SimpleFeatureReExtractor:
    def __init__(self, statistics_file='extracted_features_seq/feature_statistics.csv',
                 output_dir='extracted_features_seq', root_dir=None, threads=4,
                 conda_env='genome_analysis'):
        """
        初始化简化版特征重新提取器
        
        Args:
            statistics_file: 特征统计文件路径
            output_dir: 输出目录
            root_dir: 基因组数据根目录
            threads: 线程数
            conda_env: conda环境名称
        """
        self.statistics_file = Path(statistics_file)
        self.output_dir = Path(output_dir)
        self.root_dir = Path(root_dir) if root_dir else Path.cwd()
        self.threads = max(1, min(threads, 16))
        self.conda_env = conda_env
        
        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'need_reextraction': 0,
            'successful_reextraction': 0,
            'failed_reextraction': 0,
            'skipped': 0
        }
        
        logger.info(f"初始化简化版特征重新提取器: 线程数={self.threads}, conda环境={self.conda_env}")
    
    def load_statistics(self) -> List[Dict[str, Any]]:
        """
        加载特征统计文件
        
        Returns:
            list: 统计数据列表
        """
        statistics = []
        try:
            with open(self.statistics_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 转换数值字段
                    try:
                        row['CDS_count'] = int(row.get('CDS_count', 0))
                        row['tRNA_count'] = int(row.get('tRNA_count', 0))
                        row['rRNA_count'] = int(row.get('rRNA_count', 0))
                    except ValueError:
                        row['CDS_count'] = 0
                        row['tRNA_count'] = 0
                        row['rRNA_count'] = 0
                    
                    statistics.append(row)
            
            self.stats['total_genomes'] = len(statistics)
            logger.info(f"成功加载 {len(statistics)} 个基因组的统计信息")
            return statistics
        except FileNotFoundError:
            logger.error(f"统计文件未找到: {self.statistics_file}")
            raise
        except Exception as e:
            logger.error(f"加载统计文件失败: {e}")
            raise
    
    def identify_genomes_for_reextraction(self, statistics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别需要重新提取的基因组
        
        Args:
            statistics: 统计数据列表
            
        Returns:
            list: 需要重新提取的基因组列表
        """
        genomes_to_reextract = []
        
        for row in statistics:
            genome_id = row.get('genome_id', '').strip()
            if not genome_id:
                continue
            
            cds_count = row.get('CDS_count', 0)
            trna_count = row.get('tRNA_count', 0)
            rrna_count = row.get('rRNA_count', 0)
            status = row.get('status', '').strip()
            
            # 检查是否需要重新提取（任何一个为0就重新提取）
            needs_reextraction = (
                cds_count == 0 or 
                trna_count == 0 or 
                rrna_count == 0 or
                status != 'success'
            )
            
            if needs_reextraction:
                # 尝试从genome_id推断kingdom
                kingdom = self.infer_kingdom_from_id(genome_id)
                genomes_to_reextract.append({
                    'genome_id': genome_id,
                    'kingdom': kingdom,
                    'original_cds': cds_count,
                    'original_trna': trna_count,
                    'original_rrna': rrna_count,
                    'original_status': status
                })
        
        self.stats['need_reextraction'] = len(genomes_to_reextract)
        logger.info(f"识别出 {len(genomes_to_reextract)} 个需要重新提取的基因组")
        return genomes_to_reextract
    
    def infer_kingdom_from_id(self, genome_id: str) -> str:
        """
        从基因组ID推断kingdom分类
        
        Args:
            genome_id: 基因组ID
            
        Returns:
            str: 推断的kingdom
        """
        # 尝试在现有目录结构中查找
        for kingdom in KINGDOM_DIR_MAPPING.values():
            kingdom_dir = self.root_dir / kingdom
            if kingdom_dir.exists():
                genome_dir = kingdom_dir / genome_id
                if genome_dir.exists():
                    return kingdom
        
        # 默认返回Bacteria（最常见的情况）
        return 'Bacteria'
    
    def create_temp_metadata_file(self, genomes_to_reextract: List[Dict[str, Any]]) -> Path:
        """
        创建临时元数据文件用于重新提取
        
        Args:
            genomes_to_reextract: 需要重新提取的基因组列表
            
        Returns:
            Path: 临时元数据文件路径
        """
        temp_metadata = Path('temp_reextraction_metadata.csv')
        
        with open(temp_metadata, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['genome_id', 'kingdom'])
            for genome_info in genomes_to_reextract:
                writer.writerow([genome_info['genome_id'], genome_info['kingdom']])
        
        logger.info(f"创建临时元数据文件: {temp_metadata}")
        return temp_metadata
    
    def run_genome_feature_extractor(self, metadata_file: Path) -> bool:
        """
        运行genome_feature_extractor.py
        
        Args:
            metadata_file: 元数据文件路径
            
        Returns:
            bool: 是否成功运行
        """
        try:
            # 构造命令
            cmd = [
                'conda', 'run', '-n', self.conda_env,
                'python', 'genome_feature_extractor.py',
                str(metadata_file),
                '--output_dir', str(self.output_dir),
                '--threads', str(self.threads),
                '--log_level', 'INFO'
            ]
            
            if self.root_dir != Path.cwd():
                cmd.extend(['--root_dir', str(self.root_dir)])
            
            logger.info(f"运行命令: {' '.join(cmd)}")
            
            # 运行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            if result.returncode == 0:
                logger.info("genome_feature_extractor.py 运行成功")
                logger.debug(f"输出: {result.stdout}")
                return True
            else:
                logger.error(f"genome_feature_extractor.py 运行失败，返回码: {result.returncode}")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("genome_feature_extractor.py 运行超时")
            return False
        except Exception as e:
            logger.error(f"运行genome_feature_extractor.py时出错: {e}")
            return False
    
    def update_statistics_from_new_results(self) -> int:
        """
        从新生成的统计文件更新原始统计文件
        
        Returns:
            int: 更新的基因组数量
        """
        try:
            new_stats_file = self.output_dir / 'feature_statistics.csv'
            if not new_stats_file.exists():
                logger.warning("未找到新的统计文件")
                return 0
            
            # 读取新的统计结果
            new_results = {}
            with open(new_stats_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    genome_id = row['genome_id']
                    new_results[genome_id] = {
                        'CDS_count': int(row.get('CDS_count', 0)),
                        'tRNA_count': int(row.get('tRNA_count', 0)),
                        'rRNA_count': int(row.get('rRNA_count', 0)),
                        'status': row.get('status', 'unknown'),
                        'error': row.get('error', '')
                    }
            
            # 读取原始统计文件
            original_stats = self.load_statistics()
            
            # 备份原文件
            backup_file = self.statistics_file.with_suffix('.csv.backup')
            shutil.copy2(self.statistics_file, backup_file)
            logger.info(f"原统计文件已备份到: {backup_file}")
            
            # 更新统计数据
            updated_count = 0
            for row in original_stats:
                genome_id = row['genome_id']
                if genome_id in new_results:
                    new_data = new_results[genome_id]
                    row['CDS_count'] = new_data['CDS_count']
                    row['tRNA_count'] = new_data['tRNA_count']
                    row['rRNA_count'] = new_data['rRNA_count']
                    row['status'] = new_data['status']
                    row['error'] = new_data['error']
                    updated_count += 1
            
            # 写回更新后的统计文件
            with open(self.statistics_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(original_stats)
            
            logger.info(f"已更新 {updated_count} 个基因组的统计信息")
            return updated_count
            
        except Exception as e:
            logger.error(f"更新统计文件失败: {e}")
            return 0

    def generate_reextraction_report(self, genomes_to_reextract: List[Dict[str, Any]],
                                   updated_count: int):
        """
        生成重新提取报告

        Args:
            genomes_to_reextract: 需要重新提取的基因组列表
            updated_count: 实际更新的基因组数量
        """
        try:
            report_file = self.output_dir / 'reextraction_report.txt'

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("基因组特征重新提取报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总基因组数: {self.stats['total_genomes']}\n")
                f.write(f"需要重新提取: {self.stats['need_reextraction']}\n")
                f.write(f"实际更新: {updated_count}\n")
                f.write(f"conda环境: {self.conda_env}\n\n")

                f.write("需要重新提取的基因组列表:\n")
                f.write("-" * 40 + "\n")
                for genome_info in genomes_to_reextract:
                    f.write(f"  {genome_info['genome_id']} ({genome_info['kingdom']}): "
                           f"原CDS={genome_info['original_cds']}, "
                           f"原tRNA={genome_info['original_trna']}, "
                           f"原rRNA={genome_info['original_rrna']}\n")

            logger.info(f"重新提取报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"生成重新提取报告失败: {e}")

    def cleanup_temp_files(self, temp_metadata: Path):
        """
        清理临时文件

        Args:
            temp_metadata: 临时元数据文件路径
        """
        try:
            if temp_metadata.exists():
                temp_metadata.unlink()
                logger.info(f"已删除临时文件: {temp_metadata}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {e}")

    def run(self):
        """
        运行特征重新提取流程
        """
        temp_metadata = None
        try:
            start_time = time.time()

            # 加载统计数据
            logger.info("加载特征统计文件...")
            original_statistics = self.load_statistics()

            # 识别需要重新提取的基因组
            logger.info("识别需要重新提取的基因组...")
            genomes_to_reextract = self.identify_genomes_for_reextraction(original_statistics)

            if not genomes_to_reextract:
                logger.info("没有需要重新提取的基因组")
                return

            # 创建临时元数据文件
            logger.info("创建临时元数据文件...")
            temp_metadata = self.create_temp_metadata_file(genomes_to_reextract)

            # 运行genome_feature_extractor.py
            logger.info("运行genome_feature_extractor.py...")
            success = self.run_genome_feature_extractor(temp_metadata)

            if not success:
                logger.error("genome_feature_extractor.py运行失败")
                return

            # 更新统计文件
            logger.info("更新统计文件...")
            updated_count = self.update_statistics_from_new_results()

            # 生成报告
            logger.info("生成重新提取报告...")
            self.generate_reextraction_report(genomes_to_reextract, updated_count)

            # 输出摘要
            elapsed_time = time.time() - start_time
            logger.info("=" * 60)
            logger.info("重新提取完成!")
            logger.info(f"总用时: {elapsed_time:.1f} 秒")
            logger.info(f"总基因组数: {self.stats['total_genomes']}")
            logger.info(f"需要重新提取: {self.stats['need_reextraction']}")
            logger.info(f"实际更新: {updated_count}")

            if self.stats['need_reextraction'] > 0:
                success_rate = updated_count / self.stats['need_reextraction'] * 100
                logger.info(f"成功率: {success_rate:.1f}%")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"运行特征重新提取流程失败: {e}", exc_info=True)
            raise
        finally:
            # 清理临时文件
            if temp_metadata:
                self.cleanup_temp_files(temp_metadata)


def main():
    parser = argparse.ArgumentParser(
        description='简化版基因组特征重新提取器 - 重新提取计数为0的基因组特征',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--statistics_file', default='extracted_features_seq/feature_statistics.csv',
                       help='特征统计文件路径')
    parser.add_argument('--output_dir', default='extracted_features_seq',
                       help='输出目录')
    parser.add_argument('--root_dir', default=None,
                       help='基因组数据根目录')
    parser.add_argument('--threads', type=int, default=4,
                       help='线程数')
    parser.add_argument('--conda_env', default='genome_analysis',
                       help='conda环境名称')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))

    # 创建重新提取器并运行
    reextractor = SimpleFeatureReExtractor(
        statistics_file=args.statistics_file,
        output_dir=args.output_dir,
        root_dir=args.root_dir,
        threads=args.threads,
        conda_env=args.conda_env
    )

    reextractor.run()


if __name__ == '__main__':
    main()
