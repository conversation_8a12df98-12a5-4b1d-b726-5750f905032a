# 基因组特征重新提取器使用指南

## 概述

`simple_feature_re_extractor.py` 是一个专门用于重新提取基因组特征的工具，它会分析 `feature_statistics.csv` 文件，找出CDS、tRNA或rRNA计数为0的基因组，然后重新运行特征提取流程。

## 主要功能

1. **智能识别**：自动识别需要重新提取的基因组（任何一个特征类型计数为0）
2. **批量处理**：一次性处理所有需要重新提取的基因组
3. **conda环境支持**：在指定的conda环境中运行genome_feature_extractor.py
4. **自动更新**：重新提取完成后自动更新统计文件
5. **详细报告**：生成完整的重新提取报告和日志
6. **备份保护**：自动备份原始统计文件

## 测试结果

已通过测试验证：
- 测试了5个需要重新提取的基因组
- 100%成功率
- 用时15.4秒
- 成功生成CDS和tRNA特征文件

## 使用方法

### 基本用法（处理完整的feature_statistics.csv）

```bash
conda run -n genome_analysis python simple_feature_re_extractor.py
```

### 高级用法

```bash
conda run -n genome_analysis python simple_feature_re_extractor.py \
    --statistics_file extracted_features_seq/feature_statistics.csv \
    --output_dir extracted_features_seq \
    --threads 4 \
    --conda_env genome_analysis \
    --log_level INFO
```

### 参数说明

- `--statistics_file`: 特征统计文件路径（默认: extracted_features_seq/feature_statistics.csv）
- `--output_dir`: 输出目录（默认: extracted_features_seq）
- `--root_dir`: 基因组数据根目录（默认: 当前目录）
- `--threads`: 线程数（默认: 4）
- `--conda_env`: conda环境名称（默认: genome_analysis）
- `--log_level`: 日志级别（DEBUG, INFO, WARNING, ERROR）

## 当前状态

根据分析，当前有 **3509个基因组** 需要重新提取特征：
- 这些基因组的CDS、tRNA或rRNA计数至少有一个为0
- 包括各种微生物基因组（细菌、古菌、真菌等）

## 处理建议

### 1. 分批处理（推荐）

由于需要处理的基因组数量较多（3509个），建议分批处理：

```bash
# 创建分批脚本
python -c "
import csv
import math

# 读取需要重新提取的基因组
genomes = []
with open('extracted_features_seq/feature_statistics.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        cds = int(row.get('CDS_count', 0))
        trna = int(row.get('tRNA_count', 0))
        rrna = int(row.get('rRNA_count', 0))
        if cds == 0 or trna == 0 or rrna == 0:
            genomes.append(row)

# 分成10批
batch_size = math.ceil(len(genomes) / 10)
for i in range(10):
    start = i * batch_size
    end = min((i + 1) * batch_size, len(genomes))
    batch_genomes = genomes[start:end]
    
    filename = f'batch_{i+1}_feature_statistics.csv'
    with open(filename, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error'])
        writer.writeheader()
        writer.writerows(batch_genomes)
    
    print(f'创建批次 {i+1}: {filename} ({len(batch_genomes)} 个基因组)')
"

# 然后逐批处理
for i in {1..10}; do
    echo "处理批次 $i..."
    conda run -n genome_analysis python simple_feature_re_extractor.py \
        --statistics_file batch_${i}_feature_statistics.csv \
        --threads 4 \
        --log_level INFO
done
```

### 2. 一次性处理

如果系统资源充足，也可以一次性处理：

```bash
conda run -n genome_analysis python simple_feature_re_extractor.py \
    --threads 8 \
    --log_level INFO
```

## 输出文件

处理完成后会生成以下文件：

```
extracted_features_seq/
├── feature_statistics.csv          # 更新后的统计文件
├── feature_statistics.csv.backup   # 原始文件备份
├── reextraction_report.txt         # 重新提取报告
├── CDS/                            # 新提取的CDS序列
├── tRNA/                           # 新提取的tRNA序列
├── rRNA/                           # 新提取的rRNA序列
└── simple_feature_re_extractor.log # 详细日志
```

## 监控和日志

### 实时监控

```bash
# 监控日志
tail -f simple_feature_re_extractor.log

# 监控进度
watch -n 5 "tail -20 simple_feature_re_extractor.log"
```

### 检查结果

```bash
# 检查更新后的统计
python -c "
import csv
total = 0
zero_counts = 0
with open('extracted_features_seq/feature_statistics.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        total += 1
        cds = int(row.get('CDS_count', 0))
        trna = int(row.get('tRNA_count', 0))
        rrna = int(row.get('rRNA_count', 0))
        if cds == 0 or trna == 0 or rrna == 0:
            zero_counts += 1

print(f'总基因组数: {total}')
print(f'仍有零计数的基因组: {zero_counts}')
print(f'成功修复的基因组: {3509 - zero_counts}')
"
```

## 性能估算

基于测试结果：
- 5个基因组用时15.4秒
- 平均每个基因组约3秒
- 3509个基因组预计需要约3小时

实际时间可能因以下因素而变化：
- 基因组大小
- 系统性能
- 网络连接（如果需要下载数据）
- 并发线程数

## 故障排除

### 1. 内存不足
```bash
# 减少线程数
--threads 2
```

### 2. 网络问题
```bash
# 增加重试和延迟
# 在genome_feature_extractor.py中调整网络参数
```

### 3. 磁盘空间不足
```bash
# 检查磁盘空间
df -h

# 清理临时文件
find . -name "*.tmp" -delete
```

### 4. conda环境问题
```bash
# 验证环境
conda info --envs
conda list -n genome_analysis
```

## 注意事项

1. **备份重要**：脚本会自动备份原始统计文件，但建议手动备份整个目录
2. **资源监控**：处理大量基因组时注意监控CPU、内存和磁盘使用
3. **网络稳定**：确保网络连接稳定，避免下载中断
4. **权限检查**：确保对输出目录有写权限

## 预期结果

成功运行后：
- 大部分基因组的特征计数应该得到改善
- 一些基因组可能仍然有0计数（这是正常的，某些基因组确实缺少特定类型的基因）
- 生成详细的处理报告和统计信息

## 联系支持

如遇到问题，请检查：
1. 日志文件中的详细错误信息
2. conda环境是否正确配置
3. 磁盘空间是否充足
4. 网络连接是否稳定
