#!/usr/bin/env python3
"""
Script to extract accession and kingdom information for failed downloads
from download_summary.tsv and save as a CSV file.
"""

import csv
import sys
from pathlib import Path

def extract_failed_accessions(input_file, output_file):
    """
    Extract accession and kingdom for failed downloads from TSV and save as CSV.
    
    Args:
        input_file (str): Path to the input TSV file
        output_file (str): Path to the output CSV file
    """
    failed_entries = []
    
    try:
        with open(input_file, 'r', newline='', encoding='utf-8') as tsv_file:
            # Use tab delimiter for TSV
            reader = csv.DictReader(tsv_file, delimiter='\t')
            
            # Process each row
            for row in reader:
                # Check if status is 'fail'
                if row.get('status') == 'fail':
                    # Extract accession and kingdom
                    accession = row.get('accession', '')
                    kingdom = row.get('kingdom', '')
                    failed_entries.append({'accession': accession, 'kingdom': kingdom})
        
        # Write to CSV file
        with open(output_file, 'w', newline='', encoding='utf-8') as csv_file:
            fieldnames = ['accession', 'kingdom']
            writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
            
            # Write header
            writer.writeheader()
            
            # Write data rows
            for entry in failed_entries:
                writer.writerow(entry)
        
        print(f"Successfully extracted {len(failed_entries)} failed entries.")
        print(f"Output saved to: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error processing files: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    # Define input and output file paths
    input_path = Path("extracted_features_seq/protein_seq/download_summary.tsv")
    output_path = Path("failed_accessions_kingdom.csv")
    
    # Check if input file exists
    if not input_path.exists():
        print(f"Error: Input file '{input_path}' does not exist.", file=sys.stderr)
        sys.exit(1)
    
    # Extract failed accessions and save to CSV
    extract_failed_accessions(input_path, output_path)

if __name__ == "__main__":
    main()
