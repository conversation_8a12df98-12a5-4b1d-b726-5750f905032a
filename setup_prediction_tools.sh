#!/bin/bash
"""
预测工具安装脚本
用于在conda环境中安装基因组特征预测所需的工具

主要工具：
- Prodigal: CDS预测
- tRNAscan-SE: tRNA预测
- Barrnap: rRNA预测
- BioPython: 序列处理

作者：AI Assistant
日期：2025-08-20
版本：1.0
"""

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查conda是否可用
check_conda() {
    if ! command -v conda &> /dev/null; then
        log_error "conda未找到，请先安装Anaconda或Miniconda"
        exit 1
    fi
    log_success "conda已找到"
}

# 检查或创建conda环境
setup_conda_env() {
    local env_name="genome_analysis"
    
    log_info "检查conda环境: $env_name"
    
    if conda env list | grep -q "^$env_name "; then
        log_success "conda环境 $env_name 已存在"
    else
        log_info "创建conda环境: $env_name"
        conda create -n $env_name python=3.8 -y
        log_success "conda环境 $env_name 创建成功"
    fi
    
    log_info "激活conda环境: $env_name"
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate $env_name
    log_success "conda环境已激活"
}

# 安装基础Python包
install_python_packages() {
    log_info "安装基础Python包..."
    
    # 安装BioPython和其他必需包
    pip install biopython requests pandas numpy
    
    log_success "Python包安装完成"
}

# 安装Prodigal
install_prodigal() {
    log_info "安装Prodigal (CDS预测工具)..."
    
    if command -v prodigal &> /dev/null; then
        log_success "Prodigal已安装"
        prodigal -v
        return
    fi
    
    # 尝试通过conda安装
    if conda install -c bioconda prodigal -y; then
        log_success "Prodigal通过conda安装成功"
    else
        log_warning "conda安装Prodigal失败，尝试其他方法"
        
        # 尝试通过apt安装（Ubuntu/Debian）
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y prodigal
            log_success "Prodigal通过apt安装成功"
        else
            log_error "无法安装Prodigal，请手动安装"
        fi
    fi
}

# 安装tRNAscan-SE
install_trnascan() {
    log_info "安装tRNAscan-SE (tRNA预测工具)..."
    
    if command -v tRNAscan-SE &> /dev/null; then
        log_success "tRNAscan-SE已安装"
        tRNAscan-SE -h | head -5
        return
    fi
    
    # 尝试通过conda安装
    if conda install -c bioconda trnascan-se -y; then
        log_success "tRNAscan-SE通过conda安装成功"
    else
        log_warning "conda安装tRNAscan-SE失败"
        log_info "请手动从 http://lowelab.ucsc.edu/tRNAscan-SE/ 下载安装"
    fi
}

# 安装Barrnap
install_barrnap() {
    log_info "安装Barrnap (rRNA预测工具)..."
    
    if command -v barrnap &> /dev/null; then
        log_success "Barrnap已安装"
        barrnap --version
        return
    fi
    
    # 尝试通过conda安装
    if conda install -c bioconda barrnap -y; then
        log_success "Barrnap通过conda安装成功"
    else
        log_warning "conda安装Barrnap失败"
        
        # 尝试通过apt安装
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y barrnap
            log_success "Barrnap通过apt安装成功"
        else
            log_error "无法安装Barrnap，请手动安装"
        fi
    fi
}

# 安装ARAGORN（备用tRNA预测工具）
install_aragorn() {
    log_info "安装ARAGORN (备用tRNA预测工具)..."
    
    if command -v aragorn &> /dev/null; then
        log_success "ARAGORN已安装"
        return
    fi
    
    # 尝试通过conda安装
    if conda install -c bioconda aragorn -y; then
        log_success "ARAGORN通过conda安装成功"
    else
        log_warning "conda安装ARAGORN失败，跳过"
    fi
}

# 验证安装
verify_installation() {
    log_info "验证工具安装..."
    
    local tools=("prodigal" "tRNAscan-SE" "barrnap")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if command -v $tool &> /dev/null; then
            log_success "$tool ✓"
        else
            log_error "$tool ✗"
            missing_tools+=($tool)
        fi
    done
    
    if [ ${#missing_tools[@]} -eq 0 ]; then
        log_success "所有必需工具都已安装"
        return 0
    else
        log_error "以下工具未安装: ${missing_tools[*]}"
        return 1
    fi
}

# 创建测试脚本
create_test_script() {
    log_info "创建测试脚本..."
    
    cat > test_prediction_tools.py << 'EOF'
#!/usr/bin/env python3
"""
预测工具测试脚本
"""

import subprocess
import sys

def test_tool(tool_name, command):
    """测试单个工具"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        if result.returncode == 0 or "usage" in result.stderr.lower() or "help" in result.stderr.lower():
            print(f"✓ {tool_name}: 可用")
            return True
        else:
            print(f"✗ {tool_name}: 不可用 - {result.stderr[:100]}")
            return False
    except subprocess.TimeoutExpired:
        print(f"✗ {tool_name}: 超时")
        return False
    except FileNotFoundError:
        print(f"✗ {tool_name}: 未找到")
        return False
    except Exception as e:
        print(f"✗ {tool_name}: 错误 - {e}")
        return False

def main():
    """主测试函数"""
    print("测试预测工具...")
    print("=" * 40)
    
    tools = [
        ("Prodigal", ["prodigal", "-h"]),
        ("tRNAscan-SE", ["tRNAscan-SE", "-h"]),
        ("Barrnap", ["barrnap", "--help"]),
        ("Python BioPython", [sys.executable, "-c", "import Bio; print('BioPython version:', Bio.__version__)"])
    ]
    
    success_count = 0
    for tool_name, command in tools:
        if test_tool(tool_name, command):
            success_count += 1
    
    print("=" * 40)
    print(f"测试结果: {success_count}/{len(tools)} 工具可用")
    
    if success_count == len(tools):
        print("✓ 所有工具都可用，可以运行enhanced_feature_predictor.py")
        return 0
    else:
        print("✗ 部分工具不可用，请检查安装")
        return 1

if __name__ == "__main__":
    sys.exit(main())
EOF

    chmod +x test_prediction_tools.py
    log_success "测试脚本已创建: test_prediction_tools.py"
}

# 主函数
main() {
    log_info "开始安装基因组特征预测工具..."
    
    # 检查conda
    check_conda
    
    # 设置conda环境
    setup_conda_env
    
    # 安装Python包
    install_python_packages
    
    # 安装预测工具
    install_prodigal
    install_trnascan
    install_barrnap
    install_aragorn
    
    # 验证安装
    if verify_installation; then
        log_success "所有工具安装完成"
    else
        log_warning "部分工具安装失败，但可以继续使用"
    fi
    
    # 创建测试脚本
    create_test_script
    
    log_info "安装完成！"
    log_info "请运行以下命令测试工具："
    log_info "conda activate genome_analysis"
    log_info "python test_prediction_tools.py"
    log_info ""
    log_info "然后可以运行增强版特征预测器："
    log_info "conda run -n genome_analysis python enhanced_feature_predictor.py"
}

# 运行主函数
main "$@"
