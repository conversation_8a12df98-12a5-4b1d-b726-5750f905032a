#!/usr/bin/env python3
"""
基因组特征重新提取器
用于重新提取feature_statistics.csv中CDS、tRNA或rRNA计数为0的基因组特征

主要功能：
1. 分析feature_statistics.csv找出需要重新提取的基因组
2. 使用优化的提取策略重新处理这些基因组
3. 更新统计文件和输出目录
4. 支持断点续传和错误恢复

作者：AI Assistant
日期：2025-08-20
版本：1.0
"""

import os
import sys
import csv
import gzip
import json
import logging
import argparse
import time
import tempfile
import shutil
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any

# 简化的kingdom映射，避免依赖外部模块
KINGDOM_DIR_MAPPING = {
    'Algae': 'Algae',
    'Fungi': 'Fungi',
    'Bacteria': 'Bacteria',
    'Archaea': 'Archaea',
    'Eukaryota': 'Eukaryota',
    'Viruses': 'Viruses',
    'Plant': 'Plant',
    'Protist': 'Protist'
}

# 由于依赖问题，我们将创建一个简化版本，不依赖原始的GenomeFeatureExtractor
# 而是直接实现核心功能

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('feature_re_extractor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class FeatureReExtractor:
    def __init__(self, statistics_file='extracted_features_seq/feature_statistics.csv',
                 output_dir='extracted_features_seq', root_dir=None, threads=4,
                 enable_prediction=True, force_reextract=False):
        """
        初始化特征重新提取器
        
        Args:
            statistics_file: 特征统计文件路径
            output_dir: 输出目录
            root_dir: 基因组数据根目录
            threads: 线程数
            enable_prediction: 是否启用特征预测
            force_reextract: 是否强制重新提取所有文件
        """
        self.statistics_file = Path(statistics_file)
        self.output_dir = Path(output_dir)
        self.root_dir = Path(root_dir) if root_dir else Path.cwd()
        self.threads = max(1, min(threads, 16))
        self.enable_prediction = enable_prediction
        self.force_reextract = force_reextract
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True, parents=True)
        for feature_type in ['CDS', 'tRNA', 'rRNA']:
            (self.output_dir / feature_type).mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'need_reextraction': 0,
            'successful_reextraction': 0,
            'failed_reextraction': 0,
            'skipped': 0
        }
        
        logger.info(f"初始化特征重新提取器: 线程数={self.threads}, 预测={self.enable_prediction}")
    
    def load_statistics(self) -> List[Dict[str, Any]]:
        """
        加载特征统计文件
        
        Returns:
            list: 统计数据列表
        """
        statistics = []
        try:
            with open(self.statistics_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 转换数值字段
                    try:
                        row['CDS_count'] = int(row.get('CDS_count', 0))
                        row['tRNA_count'] = int(row.get('tRNA_count', 0))
                        row['rRNA_count'] = int(row.get('rRNA_count', 0))
                    except ValueError:
                        row['CDS_count'] = 0
                        row['tRNA_count'] = 0
                        row['rRNA_count'] = 0
                    
                    statistics.append(row)
            
            self.stats['total_genomes'] = len(statistics)
            logger.info(f"成功加载 {len(statistics)} 个基因组的统计信息")
            return statistics
        except FileNotFoundError:
            logger.error(f"统计文件未找到: {self.statistics_file}")
            raise
        except Exception as e:
            logger.error(f"加载统计文件失败: {e}")
            raise
    
    def identify_genomes_for_reextraction(self, statistics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别需要重新提取的基因组
        
        Args:
            statistics: 统计数据列表
            
        Returns:
            list: 需要重新提取的基因组列表
        """
        genomes_to_reextract = []
        
        for row in statistics:
            genome_id = row.get('genome_id', '').strip()
            if not genome_id:
                continue
            
            cds_count = row.get('CDS_count', 0)
            trna_count = row.get('tRNA_count', 0)
            rrna_count = row.get('rRNA_count', 0)
            status = row.get('status', '').strip()
            
            # 检查是否需要重新提取
            needs_reextraction = (
                self.force_reextract or
                cds_count == 0 or 
                trna_count == 0 or 
                rrna_count == 0 or
                status != 'success'
            )
            
            if needs_reextraction:
                # 尝试从genome_id推断kingdom
                kingdom = self.infer_kingdom_from_id(genome_id)
                genomes_to_reextract.append({
                    'genome_id': genome_id,
                    'kingdom': kingdom,
                    'original_cds': cds_count,
                    'original_trna': trna_count,
                    'original_rrna': rrna_count,
                    'original_status': status
                })
        
        self.stats['need_reextraction'] = len(genomes_to_reextract)
        logger.info(f"识别出 {len(genomes_to_reextract)} 个需要重新提取的基因组")
        return genomes_to_reextract
    
    def infer_kingdom_from_id(self, genome_id: str) -> str:
        """
        从基因组ID推断kingdom分类
        
        Args:
            genome_id: 基因组ID
            
        Returns:
            str: 推断的kingdom
        """
        # 尝试在现有目录结构中查找
        for kingdom in KINGDOM_DIR_MAPPING.values():
            kingdom_dir = self.root_dir / kingdom
            if kingdom_dir.exists():
                genome_dir = kingdom_dir / genome_id
                if genome_dir.exists():
                    return kingdom
        
        # 默认返回Bacteria（最常见的情况）
        return 'Bacteria'
    
    def check_existing_output(self, genome_id: str) -> Dict[str, bool]:
        """
        检查现有输出文件
        
        Args:
            genome_id: 基因组ID
            
        Returns:
            dict: 各特征类型的文件存在状态
        """
        existing = {}
        for feature_type in ['CDS', 'tRNA', 'rRNA']:
            output_file = self.output_dir / feature_type / f"{genome_id}_{feature_type}.fasta.gz"
            existing[feature_type] = output_file.exists() and output_file.stat().st_size > 0
        
        return existing
    
    def remove_existing_output(self, genome_id: str):
        """
        删除现有的输出文件以便重新生成
        
        Args:
            genome_id: 基因组ID
        """
        for feature_type in ['CDS', 'tRNA', 'rRNA']:
            output_file = self.output_dir / feature_type / f"{genome_id}_{feature_type}.fasta.gz"
            if output_file.exists():
                try:
                    output_file.unlink()
                    logger.debug(f"删除现有文件: {output_file}")
                except Exception as e:
                    logger.warning(f"删除文件失败 {output_file}: {e}")
    
    def reextract_genome_features(self, genome_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        重新提取单个基因组的特征
        
        Args:
            genome_info: 基因组信息字典
            
        Returns:
            dict: 提取结果
        """
        genome_id = genome_info['genome_id']
        kingdom = genome_info['kingdom']
        
        logger.info(f"开始重新提取基因组特征: {genome_id} ({kingdom})")
        
        try:
            # 检查现有输出
            existing = self.check_existing_output(genome_id)
            if not self.force_reextract and all(existing.values()):
                logger.info(f"所有特征文件已存在，跳过: {genome_id}")
                self.stats['skipped'] += 1
                return {
                    'genome_id': genome_id,
                    'status': 'skipped',
                    'message': 'All feature files already exist'
                }
            
            # 删除现有输出文件
            self.remove_existing_output(genome_id)
            
            # 创建特征提取器实例
            extractor = GenomeFeatureExtractor(
                root_dir=self.root_dir,
                output_dir=self.output_dir,
                threads=1,  # 单线程处理单个基因组
                enable_prediction=self.enable_prediction,
                skip_prediction=False
            )
            
            # 处理基因组
            metadata_row = {
                'genome_id': genome_id,
                'kingdom': kingdom
            }
            
            result = extractor.process_genome(metadata_row)
            
            if result['status'] == 'success':
                logger.info(f"成功重新提取: {genome_id} - "
                          f"CDS={result['CDS_count']}, tRNA={result['tRNA_count']}, rRNA={result['rRNA_count']}")
                self.stats['successful_reextraction'] += 1
            else:
                logger.error(f"重新提取失败: {genome_id} - {result.get('error', 'Unknown error')}")
                self.stats['failed_reextraction'] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"重新提取基因组 {genome_id} 时出错: {e}", exc_info=True)
            self.stats['failed_reextraction'] += 1
            return {
                'genome_id': genome_id,
                'status': 'error',
                'error': str(e)
            }
    
    def batch_reextract(self, genomes_to_reextract: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量重新提取基因组特征
        
        Args:
            genomes_to_reextract: 需要重新提取的基因组列表
            
        Returns:
            list: 提取结果列表
        """
        logger.info(f"开始批量重新提取 {len(genomes_to_reextract)} 个基因组")
        
        results = []
        start_time = time.time()
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            future_to_genome = {}
            for genome_info in genomes_to_reextract:
                future = executor.submit(self.reextract_genome_features, genome_info)
                future_to_genome[future] = genome_info
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_genome):
                genome_info = future_to_genome[future]
                genome_id = genome_info['genome_id']
                
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"处理基因组 {genome_id} 时出错: {e}")
                    results.append({
                        'genome_id': genome_id,
                        'status': 'error',
                        'error': str(e)
                    })
                    self.stats['failed_reextraction'] += 1
                
                completed += 1
                elapsed = time.time() - start_time
                progress = completed / len(genomes_to_reextract) * 100
                eta = (elapsed / completed) * (len(genomes_to_reextract) - completed) if completed > 0 else 0
                
                logger.info(f"进度: {completed}/{len(genomes_to_reextract)} ({progress:.1f}%) - "
                          f"用时: {elapsed:.1f}s, ETA: {eta:.1f}s - "
                          f"成功: {self.stats['successful_reextraction']}, "
                          f"失败: {self.stats['failed_reextraction']}, "
                          f"跳过: {self.stats['skipped']}")
        
        return results

    def update_statistics_file(self, original_statistics: List[Dict[str, Any]],
                             reextraction_results: List[Dict[str, Any]]):
        """
        更新统计文件

        Args:
            original_statistics: 原始统计数据
            reextraction_results: 重新提取结果
        """
        try:
            # 创建结果映射
            result_map = {result['genome_id']: result for result in reextraction_results}

            # 更新统计数据
            updated_statistics = []
            for row in original_statistics:
                genome_id = row['genome_id']
                if genome_id in result_map:
                    result = result_map[genome_id]
                    if result['status'] == 'success':
                        # 更新成功的结果
                        row['CDS_count'] = result.get('CDS_count', 0)
                        row['tRNA_count'] = result.get('tRNA_count', 0)
                        row['rRNA_count'] = result.get('rRNA_count', 0)
                        row['status'] = 'success'
                        row['error'] = ''
                    elif result['status'] == 'skipped':
                        # 跳过的保持原状
                        pass
                    else:
                        # 更新失败的结果
                        row['status'] = 'error'
                        row['error'] = result.get('error', 'Reextraction failed')[:100]

                updated_statistics.append(row)

            # 备份原文件
            backup_file = self.statistics_file.with_suffix('.csv.backup')
            shutil.copy2(self.statistics_file, backup_file)
            logger.info(f"原统计文件已备份到: {backup_file}")

            # 写入更新后的统计文件
            with open(self.statistics_file, 'w', newline='', encoding='utf-8') as f:
                if updated_statistics:
                    fieldnames = ['genome_id', 'CDS_count', 'tRNA_count', 'rRNA_count', 'status', 'error']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(updated_statistics)

            logger.info(f"统计文件已更新: {self.statistics_file}")

        except Exception as e:
            logger.error(f"更新统计文件失败: {e}")
            raise

    def generate_reextraction_report(self, reextraction_results: List[Dict[str, Any]]):
        """
        生成重新提取报告

        Args:
            reextraction_results: 重新提取结果
        """
        try:
            report_file = self.output_dir / 'reextraction_report.txt'

            successful = [r for r in reextraction_results if r['status'] == 'success']
            failed = [r for r in reextraction_results if r['status'] == 'error']
            skipped = [r for r in reextraction_results if r['status'] == 'skipped']

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("基因组特征重新提取报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总基因组数: {self.stats['total_genomes']}\n")
                f.write(f"需要重新提取: {self.stats['need_reextraction']}\n")
                f.write(f"成功重新提取: {self.stats['successful_reextraction']}\n")
                f.write(f"重新提取失败: {self.stats['failed_reextraction']}\n")
                f.write(f"跳过处理: {self.stats['skipped']}\n\n")

                if successful:
                    f.write("成功重新提取的基因组:\n")
                    f.write("-" * 30 + "\n")
                    for result in successful:
                        f.write(f"  {result['genome_id']}: "
                               f"CDS={result.get('CDS_count', 0)}, "
                               f"tRNA={result.get('tRNA_count', 0)}, "
                               f"rRNA={result.get('rRNA_count', 0)}\n")
                    f.write("\n")

                if failed:
                    f.write("重新提取失败的基因组:\n")
                    f.write("-" * 30 + "\n")
                    for result in failed:
                        f.write(f"  {result['genome_id']}: {result.get('error', 'Unknown error')}\n")
                    f.write("\n")

                if skipped:
                    f.write("跳过处理的基因组:\n")
                    f.write("-" * 30 + "\n")
                    for result in skipped:
                        f.write(f"  {result['genome_id']}: {result.get('message', 'Skipped')}\n")

            logger.info(f"重新提取报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"生成重新提取报告失败: {e}")

    def run(self):
        """
        运行特征重新提取流程
        """
        try:
            start_time = time.time()

            # 加载统计数据
            logger.info("加载特征统计文件...")
            original_statistics = self.load_statistics()

            # 识别需要重新提取的基因组
            logger.info("识别需要重新提取的基因组...")
            genomes_to_reextract = self.identify_genomes_for_reextraction(original_statistics)

            if not genomes_to_reextract:
                logger.info("没有需要重新提取的基因组")
                return

            # 批量重新提取
            logger.info("开始批量重新提取...")
            reextraction_results = self.batch_reextract(genomes_to_reextract)

            # 更新统计文件
            logger.info("更新统计文件...")
            self.update_statistics_file(original_statistics, reextraction_results)

            # 生成报告
            logger.info("生成重新提取报告...")
            self.generate_reextraction_report(reextraction_results)

            # 输出摘要
            elapsed_time = time.time() - start_time
            logger.info("=" * 60)
            logger.info("重新提取完成!")
            logger.info(f"总用时: {elapsed_time:.1f} 秒")
            logger.info(f"总基因组数: {self.stats['total_genomes']}")
            logger.info(f"需要重新提取: {self.stats['need_reextraction']}")
            logger.info(f"成功重新提取: {self.stats['successful_reextraction']}")
            logger.info(f"重新提取失败: {self.stats['failed_reextraction']}")
            logger.info(f"跳过处理: {self.stats['skipped']}")

            if self.stats['need_reextraction'] > 0:
                success_rate = self.stats['successful_reextraction'] / self.stats['need_reextraction'] * 100
                logger.info(f"成功率: {success_rate:.1f}%")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"运行特征重新提取流程失败: {e}", exc_info=True)
            raise


def main():
    parser = argparse.ArgumentParser(
        description='基因组特征重新提取器 - 重新提取计数为0的基因组特征',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--statistics_file', default='extracted_features_seq/feature_statistics.csv',
                       help='特征统计文件路径')
    parser.add_argument('--output_dir', default='extracted_features_seq',
                       help='输出目录')
    parser.add_argument('--root_dir', default=None,
                       help='基因组数据根目录')
    parser.add_argument('--threads', type=int, default=4,
                       help='线程数')
    parser.add_argument('--disable_prediction', action='store_true',
                       help='禁用特征预测')
    parser.add_argument('--force_reextract', action='store_true',
                       help='强制重新提取所有基因组（忽略现有文件）')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level.upper()))

    # 创建重新提取器并运行
    reextractor = FeatureReExtractor(
        statistics_file=args.statistics_file,
        output_dir=args.output_dir,
        root_dir=args.root_dir,
        threads=args.threads,
        enable_prediction=not args.disable_prediction,
        force_reextract=args.force_reextract
    )

    reextractor.run()


if __name__ == '__main__':
    main()
